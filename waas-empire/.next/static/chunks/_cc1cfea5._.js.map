{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/waas-empire/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/waas-empire/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,KASb;QATa,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF,GATa;IAUd,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/waas-empire/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,KAM6C;QAN7C,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD,GAN7C;IAOb,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 146, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/waas-empire/components/ui/navigation-menu.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as NavigationMenuPrimitive from \"@radix-ui/react-navigation-menu\"\nimport { cva } from \"class-variance-authority\"\nimport { ChevronDownIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction NavigationMenu({\n  className,\n  children,\n  viewport = true,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Root> & {\n  viewport?: boolean\n}) {\n  return (\n    <NavigationMenuPrimitive.Root\n      data-slot=\"navigation-menu\"\n      data-viewport={viewport}\n      className={cn(\n        \"group/navigation-menu relative flex max-w-max flex-1 items-center justify-center\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      {viewport && <NavigationMenuViewport />}\n    </NavigationMenuPrimitive.Root>\n  )\n}\n\nfunction NavigationMenuList({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.List>) {\n  return (\n    <NavigationMenuPrimitive.List\n      data-slot=\"navigation-menu-list\"\n      className={cn(\n        \"group flex flex-1 list-none items-center justify-center gap-1\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction NavigationMenuItem({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Item>) {\n  return (\n    <NavigationMenuPrimitive.Item\n      data-slot=\"navigation-menu-item\"\n      className={cn(\"relative\", className)}\n      {...props}\n    />\n  )\n}\n\nconst navigationMenuTriggerStyle = cva(\n  \"group inline-flex h-9 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground disabled:pointer-events-none disabled:opacity-50 data-[state=open]:hover:bg-accent data-[state=open]:text-accent-foreground data-[state=open]:focus:bg-accent data-[state=open]:bg-accent/50 focus-visible:ring-ring/50 outline-none transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1\"\n)\n\nfunction NavigationMenuTrigger({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Trigger>) {\n  return (\n    <NavigationMenuPrimitive.Trigger\n      data-slot=\"navigation-menu-trigger\"\n      className={cn(navigationMenuTriggerStyle(), \"group\", className)}\n      {...props}\n    >\n      {children}{\" \"}\n      <ChevronDownIcon\n        className=\"relative top-[1px] ml-1 size-3 transition duration-300 group-data-[state=open]:rotate-180\"\n        aria-hidden=\"true\"\n      />\n    </NavigationMenuPrimitive.Trigger>\n  )\n}\n\nfunction NavigationMenuContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Content>) {\n  return (\n    <NavigationMenuPrimitive.Content\n      data-slot=\"navigation-menu-content\"\n      className={cn(\n        \"data-[motion^=from-]:animate-in data-[motion^=to-]:animate-out data-[motion^=from-]:fade-in data-[motion^=to-]:fade-out data-[motion=from-end]:slide-in-from-right-52 data-[motion=from-start]:slide-in-from-left-52 data-[motion=to-end]:slide-out-to-right-52 data-[motion=to-start]:slide-out-to-left-52 top-0 left-0 w-full p-2 pr-2.5 md:absolute md:w-auto\",\n        \"group-data-[viewport=false]/navigation-menu:bg-popover group-data-[viewport=false]/navigation-menu:text-popover-foreground group-data-[viewport=false]/navigation-menu:data-[state=open]:animate-in group-data-[viewport=false]/navigation-menu:data-[state=closed]:animate-out group-data-[viewport=false]/navigation-menu:data-[state=closed]:zoom-out-95 group-data-[viewport=false]/navigation-menu:data-[state=open]:zoom-in-95 group-data-[viewport=false]/navigation-menu:data-[state=open]:fade-in-0 group-data-[viewport=false]/navigation-menu:data-[state=closed]:fade-out-0 group-data-[viewport=false]/navigation-menu:top-full group-data-[viewport=false]/navigation-menu:mt-1.5 group-data-[viewport=false]/navigation-menu:overflow-hidden group-data-[viewport=false]/navigation-menu:rounded-md group-data-[viewport=false]/navigation-menu:border group-data-[viewport=false]/navigation-menu:shadow group-data-[viewport=false]/navigation-menu:duration-200 **:data-[slot=navigation-menu-link]:focus:ring-0 **:data-[slot=navigation-menu-link]:focus:outline-none\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction NavigationMenuViewport({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Viewport>) {\n  return (\n    <div\n      className={cn(\n        \"absolute top-full left-0 isolate z-50 flex justify-center\"\n      )}\n    >\n      <NavigationMenuPrimitive.Viewport\n        data-slot=\"navigation-menu-viewport\"\n        className={cn(\n          \"origin-top-center bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-90 relative mt-1.5 h-[var(--radix-navigation-menu-viewport-height)] w-full overflow-hidden rounded-md border shadow md:w-[var(--radix-navigation-menu-viewport-width)]\",\n          className\n        )}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction NavigationMenuLink({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Link>) {\n  return (\n    <NavigationMenuPrimitive.Link\n      data-slot=\"navigation-menu-link\"\n      className={cn(\n        \"data-[active=true]:focus:bg-accent data-[active=true]:hover:bg-accent data-[active=true]:bg-accent/50 data-[active=true]:text-accent-foreground hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus-visible:ring-ring/50 [&_svg:not([class*='text-'])]:text-muted-foreground flex flex-col gap-1 rounded-sm p-2 text-sm transition-all outline-none focus-visible:ring-[3px] focus-visible:outline-1 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction NavigationMenuIndicator({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Indicator>) {\n  return (\n    <NavigationMenuPrimitive.Indicator\n      data-slot=\"navigation-menu-indicator\"\n      className={cn(\n        \"data-[state=visible]:animate-in data-[state=hidden]:animate-out data-[state=hidden]:fade-out data-[state=visible]:fade-in top-full z-[1] flex h-1.5 items-end justify-center overflow-hidden\",\n        className\n      )}\n      {...props}\n    >\n      <div className=\"bg-border relative top-[60%] h-2 w-2 rotate-45 rounded-tl-sm shadow-md\" />\n    </NavigationMenuPrimitive.Indicator>\n  )\n}\n\nexport {\n  NavigationMenu,\n  NavigationMenuList,\n  NavigationMenuItem,\n  NavigationMenuContent,\n  NavigationMenuTrigger,\n  NavigationMenuLink,\n  NavigationMenuIndicator,\n  NavigationMenuViewport,\n  navigationMenuTriggerStyle,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AACA;AACA;AACA;AAEA;;;;;;AAEA,SAAS,eAAe,KAOvB;QAPuB,EACtB,SAAS,EACT,QAAQ,EACR,WAAW,IAAI,EACf,GAAG,OAGJ,GAPuB;IAQtB,qBACE,6LAAC,iLAAA,CAAA,OAA4B;QAC3B,aAAU;QACV,iBAAe;QACf,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,oFACA;QAED,GAAG,KAAK;;YAER;YACA,0BAAY,6LAAC;;;;;;;;;;;AAGpB;KAtBS;AAwBT,SAAS,mBAAmB,KAGgC;QAHhC,EAC1B,SAAS,EACT,GAAG,OACuD,GAHhC;IAI1B,qBACE,6LAAC,iLAAA,CAAA,OAA4B;QAC3B,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,mBAAmB,KAGgC;QAHhC,EAC1B,SAAS,EACT,GAAG,OACuD,GAHhC;IAI1B,qBACE,6LAAC,iLAAA,CAAA,OAA4B;QAC3B,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,MAAM,6BAA6B,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACnC;AAGF,SAAS,sBAAsB,KAIgC;QAJhC,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D,GAJhC;IAK7B,qBACE,6LAAC,iLAAA,CAAA,UAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B,SAAS;QACpD,GAAG,KAAK;;YAER;YAAU;0BACX,6LAAC,2NAAA,CAAA,kBAAe;gBACd,WAAU;gBACV,eAAY;;;;;;;;;;;;AAIpB;MAlBS;AAoBT,SAAS,sBAAsB,KAGgC;QAHhC,EAC7B,SAAS,EACT,GAAG,OAC0D,GAHhC;IAI7B,qBACE,6LAAC,iLAAA,CAAA,UAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,oWACA,6hCACA;QAED,GAAG,KAAK;;;;;;AAGf;MAfS;AAiBT,SAAS,uBAAuB,KAGgC;QAHhC,EAC9B,SAAS,EACT,GAAG,OAC2D,GAHhC;IAI9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV;kBAGF,cAAA,6LAAC,iLAAA,CAAA,WAAgC;YAC/B,aAAU;YACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,sVACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MApBS;AAsBT,SAAS,mBAAmB,KAGgC;QAHhC,EAC1B,SAAS,EACT,GAAG,OACuD,GAHhC;IAI1B,qBACE,6LAAC,iLAAA,CAAA,OAA4B;QAC3B,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,ydACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,wBAAwB,KAGgC;QAHhC,EAC/B,SAAS,EACT,GAAG,OAC4D,GAHhC;IAI/B,qBACE,6LAAC,iLAAA,CAAA,YAAiC;QAChC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,gMACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC;YAAI,WAAU;;;;;;;;;;;AAGrB;MAhBS", "debugId": null}}, {"offset": {"line": 326, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/waas-empire/components/navigation.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { useUser, UserButton } from '@clerk/nextjs';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport {\n  NavigationMenu,\n  NavigationMenuContent,\n  NavigationMenuItem,\n  NavigationMenuLink,\n  NavigationMenuList,\n  NavigationMenuTrigger,\n} from '@/components/ui/navigation-menu';\n\ninterface NavigationProps {\n  variant?: 'landing' | 'dashboard';\n  showUserButton?: boolean;\n}\n\nexport function Navigation({ variant = 'landing', showUserButton = true }: NavigationProps) {\n  const { isSignedIn, isLoaded } = useUser();\n\n  const Logo = () => (\n    <Link href=\"/\" className=\"flex items-center space-x-2\">\n      <div className=\"w-8 h-8 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center\">\n        <span className=\"text-white font-bold text-sm\">W</span>\n      </div>\n      <span className=\"text-xl font-bold text-gray-900\">WaaS Empire</span>\n    </Link>\n  );\n\n  if (variant === 'dashboard') {\n    return (\n      <nav className=\"border-b bg-white shadow-sm\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <Logo />\n            \n            <NavigationMenu>\n              <NavigationMenuList>\n                <NavigationMenuItem>\n                  <NavigationMenuTrigger>Dashboard</NavigationMenuTrigger>\n                  <NavigationMenuContent>\n                    <div className=\"grid gap-3 p-6 w-[400px]\">\n                      <NavigationMenuLink asChild>\n                        <Link\n                          href=\"/agency/dashboard\"\n                          className=\"block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground\"\n                        >\n                          <div className=\"text-sm font-medium leading-none\">Overview</div>\n                          <p className=\"line-clamp-2 text-sm leading-snug text-muted-foreground\">\n                            View your agency metrics and performance\n                          </p>\n                        </Link>\n                      </NavigationMenuLink>\n                      <NavigationMenuLink asChild>\n                        <Link\n                          href=\"/agency/dashboard?tab=clients\"\n                          className=\"block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground\"\n                        >\n                          <div className=\"text-sm font-medium leading-none\">Clients</div>\n                          <p className=\"line-clamp-2 text-sm leading-snug text-muted-foreground\">\n                            Manage your client accounts and workflows\n                          </p>\n                        </Link>\n                      </NavigationMenuLink>\n                      <NavigationMenuLink asChild>\n                        <Link\n                          href=\"/agency/dashboard?tab=branding\"\n                          className=\"block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground\"\n                        >\n                          <div className=\"text-sm font-medium leading-none\">Branding</div>\n                          <p className=\"line-clamp-2 text-sm leading-snug text-muted-foreground\">\n                            Customize your white-label appearance\n                          </p>\n                        </Link>\n                      </NavigationMenuLink>\n                    </div>\n                  </NavigationMenuContent>\n                </NavigationMenuItem>\n                \n                <NavigationMenuItem>\n                  <NavigationMenuLink asChild>\n                    <Link\n                      href=\"/marketplace\"\n                      className=\"group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50\"\n                    >\n                      Marketplace\n                      <Badge variant=\"secondary\" className=\"ml-2\">\n                        New\n                      </Badge>\n                    </Link>\n                  </NavigationMenuLink>\n                </NavigationMenuItem>\n              </NavigationMenuList>\n            </NavigationMenu>\n\n            <div className=\"flex items-center space-x-4\">\n              {isLoaded && showUserButton && <UserButton />}\n            </div>\n          </div>\n        </div>\n      </nav>\n    );\n  }\n\n  // Landing page navigation\n  return (\n    <nav className=\"border-b bg-white/80 backdrop-blur-sm sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          <Logo />\n          \n          <div className=\"hidden md:flex items-center space-x-8\">\n            <NavigationMenu>\n              <NavigationMenuList>\n                <NavigationMenuItem>\n                  <NavigationMenuTrigger>Features</NavigationMenuTrigger>\n                  <NavigationMenuContent>\n                    <div className=\"grid gap-3 p-6 w-[500px] grid-cols-2\">\n                      <div className=\"space-y-3\">\n                        <h4 className=\"text-sm font-medium leading-none\">Platform</h4>\n                        <NavigationMenuLink asChild>\n                          <a\n                            href=\"#features\"\n                            className=\"block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground\"\n                          >\n                            <div className=\"text-sm font-medium leading-none\">White-Label</div>\n                            <p className=\"line-clamp-2 text-sm leading-snug text-muted-foreground\">\n                              Complete branding customization\n                            </p>\n                          </a>\n                        </NavigationMenuLink>\n                        <NavigationMenuLink asChild>\n                          <a\n                            href=\"#features\"\n                            className=\"block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground\"\n                          >\n                            <div className=\"text-sm font-medium leading-none\">Auto-Deploy</div>\n                            <p className=\"line-clamp-2 text-sm leading-snug text-muted-foreground\">\n                              One-click n8n deployment\n                            </p>\n                          </a>\n                        </NavigationMenuLink>\n                      </div>\n                      <div className=\"space-y-3\">\n                        <h4 className=\"text-sm font-medium leading-none\">Revenue</h4>\n                        <NavigationMenuLink asChild>\n                          <a\n                            href=\"#features\"\n                            className=\"block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground\"\n                          >\n                            <div className=\"text-sm font-medium leading-none\">Marketplace</div>\n                            <p className=\"line-clamp-2 text-sm leading-snug text-muted-foreground\">\n                              50% revenue sharing\n                            </p>\n                          </a>\n                        </NavigationMenuLink>\n                        <NavigationMenuLink asChild>\n                          <a\n                            href=\"#features\"\n                            className=\"block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground\"\n                          >\n                            <div className=\"text-sm font-medium leading-none\">Stripe Connect</div>\n                            <p className=\"line-clamp-2 text-sm leading-snug text-muted-foreground\">\n                              Automated payments\n                            </p>\n                          </a>\n                        </NavigationMenuLink>\n                      </div>\n                    </div>\n                  </NavigationMenuContent>\n                </NavigationMenuItem>\n                \n                <NavigationMenuItem>\n                  <NavigationMenuLink asChild>\n                    <Link\n                      href=\"/marketplace\"\n                      className=\"group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50\"\n                    >\n                      Marketplace\n                    </Link>\n                  </NavigationMenuLink>\n                </NavigationMenuItem>\n                \n                <NavigationMenuItem>\n                  <NavigationMenuLink asChild>\n                    <a\n                      href=\"#pricing\"\n                      className=\"group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50\"\n                    >\n                      Pricing\n                    </a>\n                  </NavigationMenuLink>\n                </NavigationMenuItem>\n              </NavigationMenuList>\n            </NavigationMenu>\n          </div>\n          \n          <div className=\"flex items-center space-x-4\">\n            {isLoaded && (\n              <>\n                {isSignedIn ? (\n                  <div className=\"flex items-center space-x-2\">\n                    <Link href=\"/agency/dashboard\">\n                      <Button variant=\"outline\">Dashboard</Button>\n                    </Link>\n                    {showUserButton && <UserButton />}\n                  </div>\n                ) : (\n                  <div className=\"flex items-center space-x-2\">\n                    <Link href=\"/waitlist\">\n                      <Button variant=\"outline\">Join Waitlist</Button>\n                    </Link>\n                    <Link href=\"/waitlist\">\n                      <Button>Get Started</Button>\n                    </Link>\n                  </div>\n                )}\n              </>\n            )}\n          </div>\n        </div>\n      </div>\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;;;AANA;;;;;;AAoBO,SAAS,WAAW,KAA+D;QAA/D,EAAE,UAAU,SAAS,EAAE,iBAAiB,IAAI,EAAmB,GAA/D;;IACzB,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD;IAEvC,MAAM,OAAO,kBACX,6LAAC,+JAAA,CAAA,UAAI;YAAC,MAAK;YAAI,WAAU;;8BACvB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAK,WAAU;kCAA+B;;;;;;;;;;;8BAEjD,6LAAC;oBAAK,WAAU;8BAAkC;;;;;;;;;;;;IAItD,IAAI,YAAY,aAAa;QAC3B,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;;;;sCAED,6LAAC,0IAAA,CAAA,iBAAc;sCACb,cAAA,6LAAC,0IAAA,CAAA,qBAAkB;;kDACjB,6LAAC,0IAAA,CAAA,qBAAkB;;0DACjB,6LAAC,0IAAA,CAAA,wBAAqB;0DAAC;;;;;;0DACvB,6LAAC,0IAAA,CAAA,wBAAqB;0DACpB,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,0IAAA,CAAA,qBAAkB;4DAAC,OAAO;sEACzB,cAAA,6LAAC,+JAAA,CAAA,UAAI;gEACH,MAAK;gEACL,WAAU;;kFAEV,6LAAC;wEAAI,WAAU;kFAAmC;;;;;;kFAClD,6LAAC;wEAAE,WAAU;kFAA0D;;;;;;;;;;;;;;;;;sEAK3E,6LAAC,0IAAA,CAAA,qBAAkB;4DAAC,OAAO;sEACzB,cAAA,6LAAC,+JAAA,CAAA,UAAI;gEACH,MAAK;gEACL,WAAU;;kFAEV,6LAAC;wEAAI,WAAU;kFAAmC;;;;;;kFAClD,6LAAC;wEAAE,WAAU;kFAA0D;;;;;;;;;;;;;;;;;sEAK3E,6LAAC,0IAAA,CAAA,qBAAkB;4DAAC,OAAO;sEACzB,cAAA,6LAAC,+JAAA,CAAA,UAAI;gEACH,MAAK;gEACL,WAAU;;kFAEV,6LAAC;wEAAI,WAAU;kFAAmC;;;;;;kFAClD,6LAAC;wEAAE,WAAU;kFAA0D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDASjF,6LAAC,0IAAA,CAAA,qBAAkB;kDACjB,cAAA,6LAAC,0IAAA,CAAA,qBAAkB;4CAAC,OAAO;sDACzB,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;oDACX;kEAEC,6LAAC,6HAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAY,WAAU;kEAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAStD,6LAAC;4BAAI,WAAU;sCACZ,YAAY,gCAAkB,6LAAC,0KAAA,CAAA,aAAU;;;;;;;;;;;;;;;;;;;;;;;;;;IAMtD;IAEA,0BAA0B;IAC1B,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;;;;kCAED,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,0IAAA,CAAA,iBAAc;sCACb,cAAA,6LAAC,0IAAA,CAAA,qBAAkB;;kDACjB,6LAAC,0IAAA,CAAA,qBAAkB;;0DACjB,6LAAC,0IAAA,CAAA,wBAAqB;0DAAC;;;;;;0DACvB,6LAAC,0IAAA,CAAA,wBAAqB;0DACpB,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAAmC;;;;;;8EACjD,6LAAC,0IAAA,CAAA,qBAAkB;oEAAC,OAAO;8EACzB,cAAA,6LAAC;wEACC,MAAK;wEACL,WAAU;;0FAEV,6LAAC;gFAAI,WAAU;0FAAmC;;;;;;0FAClD,6LAAC;gFAAE,WAAU;0FAA0D;;;;;;;;;;;;;;;;;8EAK3E,6LAAC,0IAAA,CAAA,qBAAkB;oEAAC,OAAO;8EACzB,cAAA,6LAAC;wEACC,MAAK;wEACL,WAAU;;0FAEV,6LAAC;gFAAI,WAAU;0FAAmC;;;;;;0FAClD,6LAAC;gFAAE,WAAU;0FAA0D;;;;;;;;;;;;;;;;;;;;;;;sEAM7E,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAAmC;;;;;;8EACjD,6LAAC,0IAAA,CAAA,qBAAkB;oEAAC,OAAO;8EACzB,cAAA,6LAAC;wEACC,MAAK;wEACL,WAAU;;0FAEV,6LAAC;gFAAI,WAAU;0FAAmC;;;;;;0FAClD,6LAAC;gFAAE,WAAU;0FAA0D;;;;;;;;;;;;;;;;;8EAK3E,6LAAC,0IAAA,CAAA,qBAAkB;oEAAC,OAAO;8EACzB,cAAA,6LAAC;wEACC,MAAK;wEACL,WAAU;;0FAEV,6LAAC;gFAAI,WAAU;0FAAmC;;;;;;0FAClD,6LAAC;gFAAE,WAAU;0FAA0D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAUnF,6LAAC,0IAAA,CAAA,qBAAkB;kDACjB,cAAA,6LAAC,0IAAA,CAAA,qBAAkB;4CAAC,OAAO;sDACzB,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;;;;;kDAML,6LAAC,0IAAA,CAAA,qBAAkB;kDACjB,cAAA,6LAAC,0IAAA,CAAA,qBAAkB;4CAAC,OAAO;sDACzB,cAAA,6LAAC;gDACC,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASX,6LAAC;wBAAI,WAAU;kCACZ,0BACC;sCACG,2BACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,6LAAC,8HAAA,CAAA,SAAM;4CAAC,SAAQ;sDAAU;;;;;;;;;;;oCAE3B,gCAAkB,6LAAC,0KAAA,CAAA,aAAU;;;;;;;;;;qDAGhC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,6LAAC,8HAAA,CAAA,SAAM;4CAAC,SAAQ;sDAAU;;;;;;;;;;;kDAE5B,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,6LAAC,8HAAA,CAAA,SAAM;sDAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW9B;GA/MgB;;QACmB,+JAAA,CAAA,UAAO;;;KAD1B", "debugId": null}}, {"offset": {"line": 982, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/waas-empire/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACZ,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACvB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 1104, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/waas-empire/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,KAGoC;QAHpC,EACd,SAAS,EACT,GAAG,OAC+C,GAHpC;IAId,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS;AAgBT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,eAAe,KAGgC;QAHhC,EACtB,SAAS,EACT,GAAG,OACmD,GAHhC;IAItB,qBACE,6LAAC,qKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS", "debugId": null}}, {"offset": {"line": 1169, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/waas-empire/components/workflow-card.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\n\ninterface Workflow {\n  id: string;\n  name: string;\n  description: string;\n  category: string;\n  price: number;\n  rating: number;\n  downloads: number;\n  author: string;\n  tags: string[];\n  thumbnail?: string;\n  workflowJson: object;\n}\n\ninterface WorkflowCardProps {\n  workflow: Workflow;\n  onDeploy?: (workflow: Workflow) => void;\n  isDeploying?: boolean;\n  variant?: 'default' | 'compact';\n}\n\nexport function WorkflowCard({ \n  workflow, \n  onDeploy, \n  isDeploying = false,\n  variant = 'default'\n}: WorkflowCardProps) {\n  const [imageError, setImageError] = useState(false);\n\n  const handleDeploy = () => {\n    if (onDeploy && !isDeploying) {\n      onDeploy(workflow);\n    }\n  };\n\n  const getCategoryIcon = (category: string) => {\n    const icons: Record<string, string> = {\n      'Marketing': '📈',\n      'Social Media': '📱',\n      'Finance': '💰',\n      'Customer Service': '🎧',\n      'Utilities': '🔧',\n      'E-commerce': '🛒',\n      'Analytics': '📊',\n      'Communication': '💬',\n      'Productivity': '⚡',\n      'Integration': '🔗'\n    };\n    return icons[category] || '⚙️';\n  };\n\n  const getCategoryColor = (category: string) => {\n    const colors: Record<string, string> = {\n      'Marketing': 'from-green-400 to-blue-500',\n      'Social Media': 'from-pink-400 to-purple-500',\n      'Finance': 'from-yellow-400 to-orange-500',\n      'Customer Service': 'from-blue-400 to-indigo-500',\n      'Utilities': 'from-gray-400 to-gray-600',\n      'E-commerce': 'from-purple-400 to-pink-500',\n      'Analytics': 'from-indigo-400 to-blue-500',\n      'Communication': 'from-green-400 to-teal-500',\n      'Productivity': 'from-orange-400 to-red-500',\n      'Integration': 'from-teal-400 to-cyan-500'\n    };\n    return colors[category] || 'from-blue-400 to-purple-500';\n  };\n\n  if (variant === 'compact') {\n    return (\n      <Card className=\"hover:shadow-md transition-shadow cursor-pointer\">\n        <CardContent className=\"p-4\">\n          <div className=\"flex items-center space-x-3\">\n            <div className={`w-12 h-12 bg-gradient-to-br ${getCategoryColor(workflow.category)} rounded-lg flex items-center justify-center text-white text-xl`}>\n              {getCategoryIcon(workflow.category)}\n            </div>\n            \n            <div className=\"flex-1 min-w-0\">\n              <h3 className=\"font-semibold text-sm truncate\">{workflow.name}</h3>\n              <p className=\"text-xs text-gray-600 truncate\">{workflow.description}</p>\n              <div className=\"flex items-center space-x-2 mt-1\">\n                <Badge variant=\"secondary\" className=\"text-xs\">\n                  {workflow.category}\n                </Badge>\n                <span className=\"text-sm font-bold text-green-600\">\n                  ${workflow.price}\n                </span>\n              </div>\n            </div>\n            \n            <Button \n              size=\"sm\" \n              onClick={handleDeploy}\n              disabled={isDeploying}\n              className=\"shrink-0\"\n            >\n              {isDeploying ? 'Deploying...' : 'Deploy'}\n            </Button>\n          </div>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <Card className=\"overflow-hidden hover:shadow-lg transition-shadow\">\n      {/* Thumbnail */}\n      <div className={`h-48 bg-gradient-to-br ${getCategoryColor(workflow.category)} flex items-center justify-center relative`}>\n        {workflow.thumbnail && !imageError ? (\n          <img\n            src={workflow.thumbnail}\n            alt={workflow.name}\n            className=\"w-full h-full object-cover\"\n            onError={() => setImageError(true)}\n          />\n        ) : (\n          <div className=\"text-white text-center\">\n            <div className=\"text-4xl mb-2\">{getCategoryIcon(workflow.category)}</div>\n            <p className=\"text-sm font-medium\">{workflow.category}</p>\n          </div>\n        )}\n        \n        {/* Category badge */}\n        <Badge \n          variant=\"secondary\" \n          className=\"absolute top-2 left-2 bg-white/90 text-gray-800\"\n        >\n          {workflow.category}\n        </Badge>\n        \n        {/* Price badge */}\n        <Badge \n          className=\"absolute top-2 right-2 bg-green-600 text-white\"\n        >\n          ${workflow.price}\n        </Badge>\n      </div>\n      \n      <CardHeader className=\"pb-2\">\n        <div className=\"flex items-start justify-between\">\n          <CardTitle className=\"text-lg line-clamp-1\">\n            {workflow.name}\n          </CardTitle>\n          <div className=\"flex items-center space-x-1 text-sm text-gray-500\">\n            <svg className=\"w-4 h-4 text-yellow-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\" />\n            </svg>\n            <span>{workflow.rating}</span>\n          </div>\n        </div>\n      </CardHeader>\n      \n      <CardContent className=\"pt-0\">\n        <p className=\"text-gray-600 text-sm mb-4 line-clamp-2\">\n          {workflow.description}\n        </p>\n        \n        {/* Author and stats */}\n        <div className=\"flex items-center justify-between mb-4\">\n          <div className=\"flex items-center space-x-2\">\n            <Avatar className=\"w-6 h-6\">\n              <AvatarImage src={`https://api.dicebear.com/7.x/initials/svg?seed=${workflow.author}`} />\n              <AvatarFallback className=\"text-xs\">\n                {workflow.author.slice(0, 2).toUpperCase()}\n              </AvatarFallback>\n            </Avatar>\n            <span className=\"text-sm text-gray-600\">{workflow.author}</span>\n          </div>\n          \n          <div className=\"text-sm text-gray-500\">\n            {workflow.downloads.toLocaleString()} downloads\n          </div>\n        </div>\n        \n        {/* Tags */}\n        <div className=\"flex flex-wrap gap-1 mb-4\">\n          {workflow.tags.slice(0, 3).map((tag) => (\n            <Badge key={tag} variant=\"outline\" className=\"text-xs\">\n              {tag}\n            </Badge>\n          ))}\n          {workflow.tags.length > 3 && (\n            <Badge variant=\"outline\" className=\"text-xs\">\n              +{workflow.tags.length - 3} more\n            </Badge>\n          )}\n        </div>\n        \n        {/* Deploy button */}\n        <Button\n          onClick={handleDeploy}\n          disabled={isDeploying}\n          className=\"w-full\"\n        >\n          {isDeploying ? (\n            <>\n              <svg className=\"animate-spin -ml-1 mr-3 h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\">\n                <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n              </svg>\n              Deploying...\n            </>\n          ) : (\n            'Deploy to n8n'\n          )}\n        </Button>\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AA6BO,SAAS,aAAa,KAKT;QALS,EAC3B,QAAQ,EACR,QAAQ,EACR,cAAc,KAAK,EACnB,UAAU,SAAS,EACD,GALS;;IAM3B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,eAAe;QACnB,IAAI,YAAY,CAAC,aAAa;YAC5B,SAAS;QACX;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,QAAgC;YACpC,aAAa;YACb,gBAAgB;YAChB,WAAW;YACX,oBAAoB;YACpB,aAAa;YACb,cAAc;YACd,aAAa;YACb,iBAAiB;YACjB,gBAAgB;YAChB,eAAe;QACjB;QACA,OAAO,KAAK,CAAC,SAAS,IAAI;IAC5B;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAiC;YACrC,aAAa;YACb,gBAAgB;YAChB,WAAW;YACX,oBAAoB;YACpB,aAAa;YACb,cAAc;YACd,aAAa;YACb,iBAAiB;YACjB,gBAAgB;YAChB,eAAe;QACjB;QACA,OAAO,MAAM,CAAC,SAAS,IAAI;IAC7B;IAEA,IAAI,YAAY,WAAW;QACzB,qBACE,6LAAC,4HAAA,CAAA,OAAI;YAAC,WAAU;sBACd,cAAA,6LAAC,4HAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAW,AAAC,+BAAkE,OAApC,iBAAiB,SAAS,QAAQ,GAAE;sCAChF,gBAAgB,SAAS,QAAQ;;;;;;sCAGpC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAkC,SAAS,IAAI;;;;;;8CAC7D,6LAAC;oCAAE,WAAU;8CAAkC,SAAS,WAAW;;;;;;8CACnE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6HAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAY,WAAU;sDAClC,SAAS,QAAQ;;;;;;sDAEpB,6LAAC;4CAAK,WAAU;;gDAAmC;gDAC/C,SAAS,KAAK;;;;;;;;;;;;;;;;;;;sCAKtB,6LAAC,8HAAA,CAAA,SAAM;4BACL,MAAK;4BACL,SAAS;4BACT,UAAU;4BACV,WAAU;sCAET,cAAc,iBAAiB;;;;;;;;;;;;;;;;;;;;;;IAM5C;IAEA,qBACE,6LAAC,4HAAA,CAAA,OAAI;QAAC,WAAU;;0BAEd,6LAAC;gBAAI,WAAW,AAAC,0BAA6D,OAApC,iBAAiB,SAAS,QAAQ,GAAE;;oBAC3E,SAAS,SAAS,IAAI,CAAC,2BACtB,6LAAC;wBACC,KAAK,SAAS,SAAS;wBACvB,KAAK,SAAS,IAAI;wBAClB,WAAU;wBACV,SAAS,IAAM,cAAc;;;;;6CAG/B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAiB,gBAAgB,SAAS,QAAQ;;;;;;0CACjE,6LAAC;gCAAE,WAAU;0CAAuB,SAAS,QAAQ;;;;;;;;;;;;kCAKzD,6LAAC,6HAAA,CAAA,QAAK;wBACJ,SAAQ;wBACR,WAAU;kCAET,SAAS,QAAQ;;;;;;kCAIpB,6LAAC,6HAAA,CAAA,QAAK;wBACJ,WAAU;;4BACX;4BACG,SAAS,KAAK;;;;;;;;;;;;;0BAIpB,6LAAC,4HAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,4HAAA,CAAA,YAAS;4BAAC,WAAU;sCAClB,SAAS,IAAI;;;;;;sCAEhB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;oCAA0B,MAAK;oCAAe,SAAQ;8CACnE,cAAA,6LAAC;wCAAK,GAAE;;;;;;;;;;;8CAEV,6LAAC;8CAAM,SAAS,MAAM;;;;;;;;;;;;;;;;;;;;;;;0BAK5B,6LAAC,4HAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,6LAAC;wBAAE,WAAU;kCACV,SAAS,WAAW;;;;;;kCAIvB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,8HAAA,CAAA,SAAM;wCAAC,WAAU;;0DAChB,6LAAC,8HAAA,CAAA,cAAW;gDAAC,KAAK,AAAC,kDAAiE,OAAhB,SAAS,MAAM;;;;;;0DACnF,6LAAC,8HAAA,CAAA,iBAAc;gDAAC,WAAU;0DACvB,SAAS,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,WAAW;;;;;;;;;;;;kDAG5C,6LAAC;wCAAK,WAAU;kDAAyB,SAAS,MAAM;;;;;;;;;;;;0CAG1D,6LAAC;gCAAI,WAAU;;oCACZ,SAAS,SAAS,CAAC,cAAc;oCAAG;;;;;;;;;;;;;kCAKzC,6LAAC;wBAAI,WAAU;;4BACZ,SAAS,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,oBAC9B,6LAAC,6HAAA,CAAA,QAAK;oCAAW,SAAQ;oCAAU,WAAU;8CAC1C;mCADS;;;;;4BAIb,SAAS,IAAI,CAAC,MAAM,GAAG,mBACtB,6LAAC,6HAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAU,WAAU;;oCAAU;oCACzC,SAAS,IAAI,CAAC,MAAM,GAAG;oCAAE;;;;;;;;;;;;;kCAMjC,6LAAC,8HAAA,CAAA,SAAM;wBACL,SAAS;wBACT,UAAU;wBACV,WAAU;kCAET,4BACC;;8CACE,6LAAC;oCAAI,WAAU;oCAAkC,MAAK;oCAAO,SAAQ;;sDACnE,6LAAC;4CAAO,WAAU;4CAAa,IAAG;4CAAK,IAAG;4CAAK,GAAE;4CAAK,QAAO;4CAAe,aAAY;;;;;;sDACxF,6LAAC;4CAAK,WAAU;4CAAa,MAAK;4CAAe,GAAE;;;;;;;;;;;;gCAC/C;;2CAIR;;;;;;;;;;;;;;;;;;AAMZ;GA3LgB;KAAA", "debugId": null}}, {"offset": {"line": 1620, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/waas-empire/components/loading.tsx"], "sourcesContent": ["import { cn } from '@/lib/utils';\n\ninterface LoadingProps {\n  size?: 'sm' | 'md' | 'lg';\n  variant?: 'spinner' | 'dots' | 'pulse';\n  className?: string;\n  text?: string;\n}\n\nexport function Loading({ \n  size = 'md', \n  variant = 'spinner', \n  className,\n  text \n}: LoadingProps) {\n  const sizeClasses = {\n    sm: 'w-4 h-4',\n    md: 'w-8 h-8',\n    lg: 'w-12 h-12'\n  };\n\n  if (variant === 'spinner') {\n    return (\n      <div className={cn('flex flex-col items-center justify-center', className)}>\n        <div\n          className={cn(\n            'animate-spin rounded-full border-2 border-gray-300 border-t-blue-600',\n            sizeClasses[size]\n          )}\n        />\n        {text && (\n          <p className=\"mt-2 text-sm text-gray-600\">{text}</p>\n        )}\n      </div>\n    );\n  }\n\n  if (variant === 'dots') {\n    return (\n      <div className={cn('flex flex-col items-center justify-center', className)}>\n        <div className=\"flex space-x-1\">\n          <div className={cn('bg-blue-600 rounded-full animate-bounce', sizeClasses[size])} style={{ animationDelay: '0ms' }} />\n          <div className={cn('bg-blue-600 rounded-full animate-bounce', sizeClasses[size])} style={{ animationDelay: '150ms' }} />\n          <div className={cn('bg-blue-600 rounded-full animate-bounce', sizeClasses[size])} style={{ animationDelay: '300ms' }} />\n        </div>\n        {text && (\n          <p className=\"mt-2 text-sm text-gray-600\">{text}</p>\n        )}\n      </div>\n    );\n  }\n\n  if (variant === 'pulse') {\n    return (\n      <div className={cn('flex flex-col items-center justify-center', className)}>\n        <div className={cn('bg-blue-600 rounded-full animate-pulse', sizeClasses[size])} />\n        {text && (\n          <p className=\"mt-2 text-sm text-gray-600\">{text}</p>\n        )}\n      </div>\n    );\n  }\n\n  return null;\n}\n\n// Full page loading component\nexport function PageLoading({ text = 'Loading...' }: { text?: string }) {\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n      <Loading size=\"lg\" text={text} />\n    </div>\n  );\n}\n\n// Card loading skeleton\nexport function CardSkeleton() {\n  return (\n    <div className=\"bg-white rounded-lg shadow-md p-6 animate-pulse\">\n      <div className=\"h-40 bg-gray-300 rounded mb-4\"></div>\n      <div className=\"h-4 bg-gray-300 rounded mb-2\"></div>\n      <div className=\"h-3 bg-gray-300 rounded mb-4\"></div>\n      <div className=\"h-8 bg-gray-300 rounded\"></div>\n    </div>\n  );\n}\n\n// Table loading skeleton\nexport function TableSkeleton({ rows = 5, cols = 4 }: { rows?: number; cols?: number }) {\n  return (\n    <div className=\"animate-pulse\">\n      <div className=\"grid gap-4\" style={{ gridTemplateColumns: `repeat(${cols}, 1fr)` }}>\n        {/* Header */}\n        {Array.from({ length: cols }).map((_, i) => (\n          <div key={`header-${i}`} className=\"h-4 bg-gray-300 rounded\"></div>\n        ))}\n        \n        {/* Rows */}\n        {Array.from({ length: rows }).map((_, rowIndex) =>\n          Array.from({ length: cols }).map((_, colIndex) => (\n            <div key={`row-${rowIndex}-col-${colIndex}`} className=\"h-4 bg-gray-200 rounded\"></div>\n          ))\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;;AASO,SAAS,QAAQ,KAKT;QALS,EACtB,OAAO,IAAI,EACX,UAAU,SAAS,EACnB,SAAS,EACT,IAAI,EACS,GALS;IAMtB,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,IAAI,YAAY,WAAW;QACzB,qBACE,6LAAC;YAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;;8BAC9D,6LAAC;oBACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,wEACA,WAAW,CAAC,KAAK;;;;;;gBAGpB,sBACC,6LAAC;oBAAE,WAAU;8BAA8B;;;;;;;;;;;;IAInD;IAEA,IAAI,YAAY,QAAQ;QACtB,qBACE,6LAAC;YAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;;8BAC9D,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C,WAAW,CAAC,KAAK;4BAAG,OAAO;gCAAE,gBAAgB;4BAAM;;;;;;sCACjH,6LAAC;4BAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C,WAAW,CAAC,KAAK;4BAAG,OAAO;gCAAE,gBAAgB;4BAAQ;;;;;;sCACnH,6LAAC;4BAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C,WAAW,CAAC,KAAK;4BAAG,OAAO;gCAAE,gBAAgB;4BAAQ;;;;;;;;;;;;gBAEpH,sBACC,6LAAC;oBAAE,WAAU;8BAA8B;;;;;;;;;;;;IAInD;IAEA,IAAI,YAAY,SAAS;QACvB,qBACE,6LAAC;YAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;;8BAC9D,6LAAC;oBAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C,WAAW,CAAC,KAAK;;;;;;gBAC7E,sBACC,6LAAC;oBAAE,WAAU;8BAA8B;;;;;;;;;;;;IAInD;IAEA,OAAO;AACT;KAvDgB;AA0DT,SAAS,YAAY,KAA0C;QAA1C,EAAE,OAAO,YAAY,EAAqB,GAA1C;IAC1B,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAQ,MAAK;YAAK,MAAM;;;;;;;;;;;AAG/B;MANgB;AAST,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;MATgB;AAYT,SAAS,cAAc,KAAwD;QAAxD,EAAE,OAAO,CAAC,EAAE,OAAO,CAAC,EAAoC,GAAxD;IAC5B,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;YAAa,OAAO;gBAAE,qBAAqB,AAAC,UAAc,OAAL,MAAK;YAAQ;;gBAE9E,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAK,GAAG,GAAG,CAAC,CAAC,GAAG,kBACpC,6LAAC;wBAAwB,WAAU;uBAAzB,AAAC,UAAW,OAAF;;;;;gBAIrB,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAK,GAAG,GAAG,CAAC,CAAC,GAAG,WACpC,MAAM,IAAI,CAAC;wBAAE,QAAQ;oBAAK,GAAG,GAAG,CAAC,CAAC,GAAG,yBACnC,6LAAC;4BAA4C,WAAU;2BAA7C,AAAC,OAAsB,OAAhB,UAAS,SAAgB,OAAT;;;;;;;;;;;;;;;;AAM7C;MAlBgB", "debugId": null}}, {"offset": {"line": 1866, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/waas-empire/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,KAA4D;QAA5D,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC,GAA5D;IACb,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 1899, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/waas-empire/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,KAEoC;QAFpC,EACd,GAAG,OAC+C,GAFpC;IAGd,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,KAOtB;QAPsB,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ,GAPsB;IAQrB,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,KAKgC;QALhC,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD,GALhC;IAMrB,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,KAIgC;QAJhC,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C,GAJhC;IAKlB,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,KAGgC;QAHhC,EACvB,SAAS,EACT,GAAG,OACoD,GAHhC;IAIvB,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,KAGgC;QAHhC,EAC5B,SAAS,EACT,GAAG,OACyD,GAHhC;IAI5B,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,KAGgC;QAHhC,EAC9B,SAAS,EACT,GAAG,OAC2D,GAHhC;IAI9B,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 2158, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/waas-empire/app/marketplace/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useUser } from '@clerk/nextjs';\nimport { useRouter } from 'next/navigation';\nimport { Navigation } from '@/components/navigation';\nimport { WorkflowCard } from '@/components/workflow-card';\nimport { CardSkeleton, PageLoading } from '@/components/loading';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\n\ninterface Workflow {\n  id: string;\n  name: string;\n  description: string;\n  category: string;\n  price: number;\n  rating: number;\n  downloads: number;\n  author: string;\n  tags: string[];\n  thumbnail?: string;\n  workflowJson: object;\n}\n\nexport default function MarketplacePage() {\n  const { isSignedIn, isLoaded } = useUser();\n  const router = useRouter();\n  const [workflows, setWorkflows] = useState<Workflow[]>([]);\n  const [filteredWorkflows, setFilteredWorkflows] = useState<Workflow[]>([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [loading, setLoading] = useState(true);\n  const [deployingId, setDeployingId] = useState<string | null>(null);\n\n  // Mock data - In real implementation, this would fetch from Google Sheets API\n  const mockWorkflows: Workflow[] = [\n    {\n      id: '1',\n      name: 'Lead Generation Automation',\n      description: 'Automatically capture leads from multiple sources and add them to your CRM',\n      category: 'Marketing',\n      price: 49.99,\n      rating: 4.8,\n      downloads: 1250,\n      author: 'AutomationPro',\n      tags: ['CRM', 'Lead Generation', 'Email'],\n      workflowJson: { nodes: [], connections: [] }\n    },\n    {\n      id: '2',\n      name: 'Social Media Scheduler',\n      description: 'Schedule and post content across multiple social media platforms',\n      category: 'Social Media',\n      price: 29.99,\n      rating: 4.6,\n      downloads: 890,\n      author: 'SocialGuru',\n      tags: ['Social Media', 'Scheduling', 'Content'],\n      workflowJson: { nodes: [], connections: [] }\n    },\n    {\n      id: '3',\n      name: 'Invoice Processing System',\n      description: 'Automatically process invoices and update accounting systems',\n      category: 'Finance',\n      price: 79.99,\n      rating: 4.9,\n      downloads: 567,\n      author: 'FinanceFlow',\n      tags: ['Accounting', 'Invoices', 'Automation'],\n      workflowJson: { nodes: [], connections: [] }\n    },\n    {\n      id: '4',\n      name: 'Customer Support Chatbot',\n      description: 'AI-powered chatbot for handling customer inquiries',\n      category: 'Customer Service',\n      price: 99.99,\n      rating: 4.7,\n      downloads: 2100,\n      author: 'ChatMaster',\n      tags: ['AI', 'Chatbot', 'Support'],\n      workflowJson: { nodes: [], connections: [] }\n    },\n    {\n      id: '5',\n      name: 'Email Marketing Campaign',\n      description: 'Create and manage automated email marketing campaigns',\n      category: 'Marketing',\n      price: 39.99,\n      rating: 4.5,\n      downloads: 1800,\n      author: 'EmailExpert',\n      tags: ['Email', 'Marketing', 'Campaigns'],\n      workflowJson: { nodes: [], connections: [] }\n    },\n    {\n      id: '6',\n      name: 'Data Backup Automation',\n      description: 'Automatically backup important data to multiple cloud services',\n      category: 'Utilities',\n      price: 19.99,\n      rating: 4.4,\n      downloads: 445,\n      author: 'BackupBuddy',\n      tags: ['Backup', 'Cloud', 'Data'],\n      workflowJson: { nodes: [], connections: [] }\n    }\n  ];\n\n  const categories = ['all', 'Marketing', 'Social Media', 'Finance', 'Customer Service', 'Utilities'];\n\n  useEffect(() => {\n    if (isLoaded && !isSignedIn) {\n      router.push('/waitlist');\n    }\n  }, [isSignedIn, isLoaded, router]);\n\n  useEffect(() => {\n    // Simulate API call to Google Sheets\n    const fetchWorkflows = async () => {\n      setLoading(true);\n      // In real implementation:\n      // const response = await fetch('/api/workflows');\n      // const data = await response.json();\n      \n      // Simulate network delay\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      setWorkflows(mockWorkflows);\n      setFilteredWorkflows(mockWorkflows);\n      setLoading(false);\n    };\n\n    if (isLoaded) {\n      fetchWorkflows();\n    }\n  }, [isLoaded]);\n\n  useEffect(() => {\n    let filtered = workflows;\n\n    // Filter by category\n    if (selectedCategory !== 'all') {\n      filtered = filtered.filter(workflow => workflow.category === selectedCategory);\n    }\n\n    // Filter by search term\n    if (searchTerm) {\n      filtered = filtered.filter(workflow =>\n        workflow.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        workflow.description.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        workflow.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))\n      );\n    }\n\n    setFilteredWorkflows(filtered);\n  }, [workflows, selectedCategory, searchTerm]);\n\n  const handleDeploy = async (workflow: Workflow) => {\n    setDeployingId(workflow.id);\n    \n    try {\n      // In real implementation, this would call the deploy API\n      const response = await fetch('/api/deploy', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          workflowId: workflow.id,\n          workflowJson: workflow.workflowJson,\n          price: workflow.price\n        }),\n      });\n\n      if (response.ok) {\n        alert(`Successfully deployed \"${workflow.name}\" to your n8n instance!`);\n      } else {\n        throw new Error('Deployment failed');\n      }\n    } catch (error) {\n      alert('Failed to deploy workflow. Please try again.');\n      console.error('Deployment error:', error);\n    } finally {\n      setDeployingId(null);\n    }\n  };\n\n  if (!isLoaded) {\n    return <PageLoading text=\"Loading marketplace...\" />;\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Navigation variant=\"dashboard\" />\n\n      {/* Page Header */}\n      <div className=\"bg-white border-b border-gray-200\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h1 className=\"text-3xl font-bold text-gray-900\">Workflow Marketplace</h1>\n              <p className=\"mt-2 text-gray-600\">Discover and deploy automation workflows to your n8n instance</p>\n            </div>\n            <Button\n              onClick={() => router.push('/agency/dashboard')}\n              variant=\"outline\"\n            >\n              <svg className=\"w-4 h-4 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path fillRule=\"evenodd\" d=\"M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z\" clipRule=\"evenodd\" />\n              </svg>\n              Back to Dashboard\n            </Button>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Search and Filters */}\n        <div className=\"mb-8\">\n          <div className=\"flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0\">\n            {/* Search */}\n            <div className=\"flex-1 max-w-lg\">\n              <div className=\"relative\">\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                  <svg className=\"h-5 w-5 text-gray-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z\" clipRule=\"evenodd\" />\n                  </svg>\n                </div>\n                <Input\n                  type=\"text\"\n                  placeholder=\"Search workflows...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"pl-10\"\n                />\n              </div>\n            </div>\n\n            {/* Category Filter */}\n            <div className=\"flex items-center space-x-4\">\n              <label className=\"text-sm font-medium text-gray-700\">Category:</label>\n              <Select value={selectedCategory} onValueChange={setSelectedCategory}>\n                <SelectTrigger className=\"w-48\">\n                  <SelectValue placeholder=\"Select category\" />\n                </SelectTrigger>\n                <SelectContent>\n                  {categories.map(category => (\n                    <SelectItem key={category} value={category}>\n                      {category === 'all' ? 'All Categories' : category}\n                    </SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n            </div>\n          </div>\n        </div>\n\n        {/* Results Count */}\n        <div className=\"mb-6\">\n          <p className=\"text-gray-600\">\n            {loading ? 'Loading...' : `${filteredWorkflows.length} workflow${filteredWorkflows.length !== 1 ? 's' : ''} found`}\n          </p>\n        </div>\n\n        {/* Workflows Grid */}\n        {loading ? (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {[...Array(6)].map((_, i) => (\n              <CardSkeleton key={i} />\n            ))}\n          </div>\n        ) : (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {filteredWorkflows.map((workflow) => (\n              <WorkflowCard\n                key={workflow.id}\n                workflow={workflow}\n                onDeploy={handleDeploy}\n                isDeploying={deployingId === workflow.id}\n              />\n            ))}\n          </div>\n        )}\n\n        {/* Empty State */}\n        {!loading && filteredWorkflows.length === 0 && (\n          <div className=\"text-center py-12\">\n            <svg className=\"w-16 h-16 mx-auto text-gray-300 mb-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fillRule=\"evenodd\" d=\"M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z\" clipRule=\"evenodd\" />\n            </svg>\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No workflows found</h3>\n            <p className=\"text-gray-500\">Try adjusting your search or filter criteria.</p>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAVA;;;;;;;;;;AA0Be,SAAS;;IACtB,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD;IACvC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAE9D,8EAA8E;IAC9E,MAAM,gBAA4B;QAChC;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,UAAU;YACV,OAAO;YACP,QAAQ;YACR,WAAW;YACX,QAAQ;YACR,MAAM;gBAAC;gBAAO;gBAAmB;aAAQ;YACzC,cAAc;gBAAE,OAAO,EAAE;gBAAE,aAAa,EAAE;YAAC;QAC7C;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,UAAU;YACV,OAAO;YACP,QAAQ;YACR,WAAW;YACX,QAAQ;YACR,MAAM;gBAAC;gBAAgB;gBAAc;aAAU;YAC/C,cAAc;gBAAE,OAAO,EAAE;gBAAE,aAAa,EAAE;YAAC;QAC7C;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,UAAU;YACV,OAAO;YACP,QAAQ;YACR,WAAW;YACX,QAAQ;YACR,MAAM;gBAAC;gBAAc;gBAAY;aAAa;YAC9C,cAAc;gBAAE,OAAO,EAAE;gBAAE,aAAa,EAAE;YAAC;QAC7C;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,UAAU;YACV,OAAO;YACP,QAAQ;YACR,WAAW;YACX,QAAQ;YACR,MAAM;gBAAC;gBAAM;gBAAW;aAAU;YAClC,cAAc;gBAAE,OAAO,EAAE;gBAAE,aAAa,EAAE;YAAC;QAC7C;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,UAAU;YACV,OAAO;YACP,QAAQ;YACR,WAAW;YACX,QAAQ;YACR,MAAM;gBAAC;gBAAS;gBAAa;aAAY;YACzC,cAAc;gBAAE,OAAO,EAAE;gBAAE,aAAa,EAAE;YAAC;QAC7C;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,UAAU;YACV,OAAO;YACP,QAAQ;YACR,WAAW;YACX,QAAQ;YACR,MAAM;gBAAC;gBAAU;gBAAS;aAAO;YACjC,cAAc;gBAAE,OAAO,EAAE;gBAAE,aAAa,EAAE;YAAC;QAC7C;KACD;IAED,MAAM,aAAa;QAAC;QAAO;QAAa;QAAgB;QAAW;QAAoB;KAAY;IAEnG,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,YAAY,CAAC,YAAY;gBAC3B,OAAO,IAAI,CAAC;YACd;QACF;oCAAG;QAAC;QAAY;QAAU;KAAO;IAEjC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,qCAAqC;YACrC,MAAM;4DAAiB;oBACrB,WAAW;oBACX,0BAA0B;oBAC1B,kDAAkD;oBAClD,sCAAsC;oBAEtC,yBAAyB;oBACzB,MAAM,IAAI;oEAAQ,CAAA,UAAW,WAAW,SAAS;;oBAEjD,aAAa;oBACb,qBAAqB;oBACrB,WAAW;gBACb;;YAEA,IAAI,UAAU;gBACZ;YACF;QACF;oCAAG;QAAC;KAAS;IAEb,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,WAAW;YAEf,qBAAqB;YACrB,IAAI,qBAAqB,OAAO;gBAC9B,WAAW,SAAS,MAAM;iDAAC,CAAA,WAAY,SAAS,QAAQ,KAAK;;YAC/D;YAEA,wBAAwB;YACxB,IAAI,YAAY;gBACd,WAAW,SAAS,MAAM;iDAAC,CAAA,WACzB,SAAS,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC3D,SAAS,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAClE,SAAS,IAAI,CAAC,IAAI;yDAAC,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;;;YAE/E;YAEA,qBAAqB;QACvB;oCAAG;QAAC;QAAW;QAAkB;KAAW;IAE5C,MAAM,eAAe,OAAO;QAC1B,eAAe,SAAS,EAAE;QAE1B,IAAI;YACF,yDAAyD;YACzD,MAAM,WAAW,MAAM,MAAM,eAAe;gBAC1C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,YAAY,SAAS,EAAE;oBACvB,cAAc,SAAS,YAAY;oBACnC,OAAO,SAAS,KAAK;gBACvB;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,AAAC,0BAAuC,OAAd,SAAS,IAAI,EAAC;YAChD,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,MAAM;YACN,QAAQ,KAAK,CAAC,qBAAqB;QACrC,SAAU;YACR,eAAe;QACjB;IACF;IAEA,IAAI,CAAC,UAAU;QACb,qBAAO,6LAAC,yHAAA,CAAA,cAAW;YAAC,MAAK;;;;;;IAC3B;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,4HAAA,CAAA,aAAU;gBAAC,SAAQ;;;;;;0BAGpB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,6LAAC;wCAAE,WAAU;kDAAqB;;;;;;;;;;;;0CAEpC,6LAAC,8HAAA,CAAA,SAAM;gCACL,SAAS,IAAM,OAAO,IAAI,CAAC;gCAC3B,SAAQ;;kDAER,6LAAC;wCAAI,WAAU;wCAAe,MAAK;wCAAe,SAAQ;kDACxD,cAAA,6LAAC;4CAAK,UAAS;4CAAU,GAAE;4CAAwI,UAAS;;;;;;;;;;;oCACxK;;;;;;;;;;;;;;;;;;;;;;;0BAOd,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;oDAAwB,MAAK;oDAAe,SAAQ;8DACjE,cAAA,6LAAC;wDAAK,UAAS;wDAAU,GAAE;wDAAmH,UAAS;;;;;;;;;;;;;;;;0DAG3J,6LAAC,6HAAA,CAAA,QAAK;gDACJ,MAAK;gDACL,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,WAAU;;;;;;;;;;;;;;;;;8CAMhB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,WAAU;sDAAoC;;;;;;sDACrD,6LAAC,8HAAA,CAAA,SAAM;4CAAC,OAAO;4CAAkB,eAAe;;8DAC9C,6LAAC,8HAAA,CAAA,gBAAa;oDAAC,WAAU;8DACvB,cAAA,6LAAC,8HAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,6LAAC,8HAAA,CAAA,gBAAa;8DACX,WAAW,GAAG,CAAC,CAAA,yBACd,6LAAC,8HAAA,CAAA,aAAU;4DAAgB,OAAO;sEAC/B,aAAa,QAAQ,mBAAmB;2DAD1B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAW7B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;sCACV,UAAU,eAAe,AAAC,GAAsC,OAApC,kBAAkB,MAAM,EAAC,aAAqD,OAA1C,kBAAkB,MAAM,KAAK,IAAI,MAAM,IAAG;;;;;;;;;;;oBAK9G,wBACC,6LAAC;wBAAI,WAAU;kCACZ;+BAAI,MAAM;yBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC,yHAAA,CAAA,eAAY,MAAM;;;;;;;;;6CAIvB,6LAAC;wBAAI,WAAU;kCACZ,kBAAkB,GAAG,CAAC,CAAC,yBACtB,6LAAC,kIAAA,CAAA,eAAY;gCAEX,UAAU;gCACV,UAAU;gCACV,aAAa,gBAAgB,SAAS,EAAE;+BAHnC,SAAS,EAAE;;;;;;;;;;oBAUvB,CAAC,WAAW,kBAAkB,MAAM,KAAK,mBACxC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;gCAAuC,MAAK;gCAAe,SAAQ;0CAChF,cAAA,6LAAC;oCAAK,UAAS;oCAAU,GAAE;oCAAmH,UAAS;;;;;;;;;;;0CAEzJ,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;;;;;;;AAMzC;GAnRwB;;QACW,+JAAA,CAAA,UAAO;QACzB,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}