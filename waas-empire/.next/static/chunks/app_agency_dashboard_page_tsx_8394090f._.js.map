{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/waas-empire/app/agency/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useUser, User<PERSON>utton } from '@clerk/nextjs';\nimport { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\n\ninterface BrandingSettings {\n  logo: string;\n  primaryColor: string;\n  secondaryColor: string;\n  companyName: string;\n}\n\ninterface Client {\n  id: string;\n  name: string;\n  email: string;\n  status: 'active' | 'inactive';\n  workflows: number;\n  revenue: number;\n  joinedDate: string;\n}\n\nexport default function AgencyDashboard() {\n  const { isSignedIn, user, isLoaded } = useUser();\n  const router = useRouter();\n  const [activeTab, setActiveTab] = useState('overview');\n  const [branding, setBranding] = useState<BrandingSettings>({\n    logo: '',\n    primaryColor: '#3B82F6',\n    secondaryColor: '#1E40AF',\n    companyName: 'Your Agency'\n  });\n\n  const [clients] = useState<Client[]>([\n    {\n      id: '1',\n      name: 'Acme Corp',\n      email: '<EMAIL>',\n      status: 'active',\n      workflows: 12,\n      revenue: 2400,\n      joinedDate: '2024-01-15'\n    },\n    {\n      id: '2',\n      name: 'TechStart Inc',\n      email: '<EMAIL>',\n      status: 'active',\n      workflows: 8,\n      revenue: 1600,\n      joinedDate: '2024-02-20'\n    },\n    {\n      id: '3',\n      name: 'Global Solutions',\n      email: '<EMAIL>',\n      status: 'inactive',\n      workflows: 5,\n      revenue: 1000,\n      joinedDate: '2024-01-08'\n    }\n  ]);\n\n  useEffect(() => {\n    if (isLoaded && !isSignedIn) {\n      router.push('/waitlist');\n    }\n  }, [isSignedIn, isLoaded, router]);\n\n  useEffect(() => {\n    // Apply CSS variables for dynamic theming\n    document.documentElement.style.setProperty('--primary-color', branding.primaryColor);\n    document.documentElement.style.setProperty('--secondary-color', branding.secondaryColor);\n  }, [branding]);\n\n  if (!isLoaded || !isSignedIn) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">Loading dashboard...</p>\n        </div>\n      </div>\n    );\n  }\n\n  const handleBrandingChange = (field: keyof BrandingSettings, value: string) => {\n    setBranding(prev => ({ ...prev, [field]: value }));\n  };\n\n  const totalRevenue = clients.reduce((sum, client) => sum + client.revenue, 0);\n  const activeClients = clients.filter(client => client.status === 'active').length;\n  const totalWorkflows = clients.reduce((sum, client) => sum + client.workflows, 0);\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center space-x-4\">\n              <h1 className=\"text-2xl font-bold\" style={{ color: branding.primaryColor }}>\n                {branding.companyName}\n              </h1>\n              <span className=\"text-sm text-gray-500\">Agency Dashboard</span>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <span className=\"text-sm text-gray-700\">\n                Welcome, {user?.firstName || 'User'}!\n              </span>\n              <UserButton\n                appearance={{\n                  elements: {\n                    avatarBox: \"w-8 h-8\"\n                  }\n                }}\n              />\n            </div>\n          </div>\n        </div>\n      </header>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Navigation Tabs */}\n        <div className=\"border-b border-gray-200 mb-8\">\n          <nav className=\"-mb-px flex space-x-8\">\n            {[\n              { id: 'overview', name: 'Overview' },\n              { id: 'clients', name: 'Clients' },\n              { id: 'branding', name: 'Branding' },\n              { id: 'billing', name: 'Billing' }\n            ].map((tab) => (\n              <button\n                key={tab.id}\n                onClick={() => setActiveTab(tab.id)}\n                className={`py-2 px-1 border-b-2 font-medium text-sm ${\n                  activeTab === tab.id\n                    ? 'border-blue-500 text-blue-600'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                }`}\n              >\n                {tab.name}\n              </button>\n            ))}\n          </nav>\n        </div>\n\n        {/* Overview Tab */}\n        {activeTab === 'overview' && (\n          <div className=\"space-y-6\">\n            {/* Stats Cards */}\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n              <div className=\"bg-white rounded-lg shadow p-6\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <div className=\"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center\">\n                      <svg className=\"w-5 h-5 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path d=\"M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z\" />\n                        <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z\" clipRule=\"evenodd\" />\n                      </svg>\n                    </div>\n                  </div>\n                  <div className=\"ml-5 w-0 flex-1\">\n                    <dl>\n                      <dt className=\"text-sm font-medium text-gray-500 truncate\">Total Revenue</dt>\n                      <dd className=\"text-lg font-medium text-gray-900\">${totalRevenue.toLocaleString()}</dd>\n                    </dl>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-white rounded-lg shadow p-6\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <div className=\"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center\">\n                      <svg className=\"w-5 h-5 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path d=\"M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z\" />\n                      </svg>\n                    </div>\n                  </div>\n                  <div className=\"ml-5 w-0 flex-1\">\n                    <dl>\n                      <dt className=\"text-sm font-medium text-gray-500 truncate\">Active Clients</dt>\n                      <dd className=\"text-lg font-medium text-gray-900\">{activeClients}</dd>\n                    </dl>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-white rounded-lg shadow p-6\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <div className=\"w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center\">\n                      <svg className=\"w-5 h-5 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z\" clipRule=\"evenodd\" />\n                      </svg>\n                    </div>\n                  </div>\n                  <div className=\"ml-5 w-0 flex-1\">\n                    <dl>\n                      <dt className=\"text-sm font-medium text-gray-500 truncate\">Total Workflows</dt>\n                      <dd className=\"text-lg font-medium text-gray-900\">{totalWorkflows}</dd>\n                    </dl>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Quick Actions */}\n            <div className=\"bg-white rounded-lg shadow p-6\">\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Quick Actions</h3>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n                <button className=\"p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left\">\n                  <div className=\"text-sm font-medium text-gray-900\">Add New Client</div>\n                  <div className=\"text-sm text-gray-500\">Create a new client account</div>\n                </button>\n                <button className=\"p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left\">\n                  <div className=\"text-sm font-medium text-gray-900\">Browse Marketplace</div>\n                  <div className=\"text-sm text-gray-500\">Find new workflows</div>\n                </button>\n                <button className=\"p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left\">\n                  <div className=\"text-sm font-medium text-gray-900\">Setup Billing</div>\n                  <div className=\"text-sm text-gray-500\">Configure Stripe Connect</div>\n                </button>\n                <button className=\"p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left\">\n                  <div className=\"text-sm font-medium text-gray-900\">View Analytics</div>\n                  <div className=\"text-sm text-gray-500\">Check performance metrics</div>\n                </button>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Clients Tab */}\n        {activeTab === 'clients' && (\n          <div className=\"bg-white rounded-lg shadow\">\n            <div className=\"px-6 py-4 border-b border-gray-200\">\n              <h3 className=\"text-lg font-medium text-gray-900\">Client Accounts</h3>\n            </div>\n            <div className=\"overflow-x-auto\">\n              <table className=\"min-w-full divide-y divide-gray-200\">\n                <thead className=\"bg-gray-50\">\n                  <tr>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Client\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Status\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Workflows\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Revenue\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Joined\n                    </th>\n                    <th className=\"relative px-6 py-3\">\n                      <span className=\"sr-only\">Actions</span>\n                    </th>\n                  </tr>\n                </thead>\n                <tbody className=\"bg-white divide-y divide-gray-200\">\n                  {clients.map((client) => (\n                    <tr key={client.id}>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <div>\n                          <div className=\"text-sm font-medium text-gray-900\">{client.name}</div>\n                          <div className=\"text-sm text-gray-500\">{client.email}</div>\n                        </div>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${\n                          client.status === 'active' \n                            ? 'bg-green-100 text-green-800' \n                            : 'bg-red-100 text-red-800'\n                        }`}>\n                          {client.status}\n                        </span>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                        {client.workflows}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                        ${client.revenue.toLocaleString()}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                        {new Date(client.joinedDate).toLocaleDateString()}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                        <button className=\"text-blue-600 hover:text-blue-900\">\n                          Manage\n                        </button>\n                      </td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n          </div>\n        )}\n\n        {/* Branding Tab */}\n        {activeTab === 'branding' && (\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <h3 className=\"text-lg font-medium text-gray-900 mb-6\">Brand Customization</h3>\n\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n              {/* Branding Form */}\n              <div className=\"space-y-6\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Company Name\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={branding.companyName}\n                    onChange={(e) => handleBrandingChange('companyName', e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    placeholder=\"Your Agency Name\"\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Logo URL\n                  </label>\n                  <input\n                    type=\"url\"\n                    value={branding.logo}\n                    onChange={(e) => handleBrandingChange('logo', e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    placeholder=\"https://your-domain.com/logo.png\"\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Primary Color\n                  </label>\n                  <div className=\"flex items-center space-x-3\">\n                    <input\n                      type=\"color\"\n                      value={branding.primaryColor}\n                      onChange={(e) => handleBrandingChange('primaryColor', e.target.value)}\n                      className=\"w-12 h-10 border border-gray-300 rounded cursor-pointer\"\n                    />\n                    <input\n                      type=\"text\"\n                      value={branding.primaryColor}\n                      onChange={(e) => handleBrandingChange('primaryColor', e.target.value)}\n                      className=\"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                      placeholder=\"#3B82F6\"\n                    />\n                  </div>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Secondary Color\n                  </label>\n                  <div className=\"flex items-center space-x-3\">\n                    <input\n                      type=\"color\"\n                      value={branding.secondaryColor}\n                      onChange={(e) => handleBrandingChange('secondaryColor', e.target.value)}\n                      className=\"w-12 h-10 border border-gray-300 rounded cursor-pointer\"\n                    />\n                    <input\n                      type=\"text\"\n                      value={branding.secondaryColor}\n                      onChange={(e) => handleBrandingChange('secondaryColor', e.target.value)}\n                      className=\"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                      placeholder=\"#1E40AF\"\n                    />\n                  </div>\n                </div>\n\n                <button\n                  className=\"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  onClick={() => alert('Branding settings saved!')}\n                >\n                  Save Branding Settings\n                </button>\n              </div>\n\n              {/* Preview */}\n              <div className=\"space-y-6\">\n                <h4 className=\"text-md font-medium text-gray-900\">Preview</h4>\n\n                {/* Header Preview */}\n                <div className=\"border border-gray-200 rounded-lg p-4\">\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <div className=\"flex items-center space-x-3\">\n                      {branding.logo && (\n                        <img src={branding.logo} alt=\"Logo\" className=\"h-8 w-8 object-contain\" />\n                      )}\n                      <h2 className=\"text-xl font-bold\" style={{ color: branding.primaryColor }}>\n                        {branding.companyName}\n                      </h2>\n                    </div>\n                  </div>\n\n                  <div className=\"space-y-3\">\n                    <button\n                      className=\"px-4 py-2 rounded text-white text-sm\"\n                      style={{ backgroundColor: branding.primaryColor }}\n                    >\n                      Primary Button\n                    </button>\n                    <button\n                      className=\"px-4 py-2 rounded text-white text-sm ml-2\"\n                      style={{ backgroundColor: branding.secondaryColor }}\n                    >\n                      Secondary Button\n                    </button>\n                  </div>\n                </div>\n\n                {/* Card Preview */}\n                <div className=\"border border-gray-200 rounded-lg p-4\">\n                  <h5 className=\"font-medium mb-2\" style={{ color: branding.primaryColor }}>\n                    Sample Dashboard Card\n                  </h5>\n                  <p className=\"text-gray-600 text-sm mb-3\">\n                    This is how your branded interface will look to clients.\n                  </p>\n                  <div className=\"flex space-x-2\">\n                    <div\n                      className=\"w-4 h-4 rounded\"\n                      style={{ backgroundColor: branding.primaryColor }}\n                    ></div>\n                    <div\n                      className=\"w-4 h-4 rounded\"\n                      style={{ backgroundColor: branding.secondaryColor }}\n                    ></div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Billing Tab */}\n        {activeTab === 'billing' && (\n          <div className=\"space-y-6\">\n            {/* Stripe Connect Status */}\n            <div className=\"bg-white rounded-lg shadow p-6\">\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Stripe Connect Setup</h3>\n\n              <div className=\"border border-yellow-200 bg-yellow-50 rounded-lg p-4 mb-6\">\n                <div className=\"flex items-center\">\n                  <svg className=\"w-5 h-5 text-yellow-600 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n                  </svg>\n                  <div>\n                    <h4 className=\"text-sm font-medium text-yellow-800\">Setup Required</h4>\n                    <p className=\"text-sm text-yellow-700\">\n                      Connect your Stripe account to start receiving payments and enable revenue sharing.\n                    </p>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"space-y-4\">\n                <button className=\"w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center justify-center\">\n                  <svg className=\"w-5 h-5 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z\" clipRule=\"evenodd\" />\n                  </svg>\n                  Connect Stripe Account\n                </button>\n\n                <div className=\"text-sm text-gray-600\">\n                  <p className=\"mb-2\">What happens when you connect:</p>\n                  <ul className=\"list-disc list-inside space-y-1 text-gray-500\">\n                    <li>Enable payment processing for your clients</li>\n                    <li>Automatic 20% platform fee collection</li>\n                    <li>50% revenue sharing on marketplace sales</li>\n                    <li>Real-time payment tracking and analytics</li>\n                  </ul>\n                </div>\n              </div>\n            </div>\n\n            {/* Billing Overview */}\n            <div className=\"bg-white rounded-lg shadow p-6\">\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Billing Overview</h3>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div className=\"border border-gray-200 rounded-lg p-4\">\n                  <h4 className=\"font-medium text-gray-900 mb-2\">Platform Fees</h4>\n                  <p className=\"text-2xl font-bold text-gray-900\">20%</p>\n                  <p className=\"text-sm text-gray-500\">On all client payments</p>\n                </div>\n\n                <div className=\"border border-gray-200 rounded-lg p-4\">\n                  <h4 className=\"font-medium text-gray-900 mb-2\">Marketplace Revenue</h4>\n                  <p className=\"text-2xl font-bold text-gray-900\">50%</p>\n                  <p className=\"text-sm text-gray-500\">Share on workflow sales</p>\n                </div>\n              </div>\n\n              <div className=\"mt-6\">\n                <h4 className=\"font-medium text-gray-900 mb-3\">Recent Transactions</h4>\n                <div className=\"text-center py-8 text-gray-500\">\n                  <svg className=\"w-12 h-12 mx-auto mb-4 text-gray-300\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h4a2 2 0 012 2v2a2 2 0 01-2 2H8a2 2 0 01-2-2v-2z\" clipRule=\"evenodd\" />\n                  </svg>\n                  <p>No transactions yet</p>\n                  <p className=\"text-sm\">Connect your Stripe account to start tracking payments</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;;;AAJA;;;;AAuBe,SAAS;;IACtB,MAAM,EAAE,UAAU,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD;IAC7C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;QACzD,MAAM;QACN,cAAc;QACd,gBAAgB;QAChB,aAAa;IACf;IAEA,MAAM,CAAC,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;QACnC;YACE,IAAI;YACJ,MAAM;YAC<PERSON>,OAAO;YACP,QAAQ;YACR,WAAW;YACX,SAAS;YACT,YAAY;QACd;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,WAAW;YACX,SAAS;YACT,YAAY;QACd;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,WAAW;YACX,SAAS;YACT,YAAY;QACd;KACD;IAED,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,YAAY,CAAC,YAAY;gBAC3B,OAAO,IAAI,CAAC;YACd;QACF;oCAAG;QAAC;QAAY;QAAU;KAAO;IAEjC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,0CAA0C;YAC1C,SAAS,eAAe,CAAC,KAAK,CAAC,WAAW,CAAC,mBAAmB,SAAS,YAAY;YACnF,SAAS,eAAe,CAAC,KAAK,CAAC,WAAW,CAAC,qBAAqB,SAAS,cAAc;QACzF;oCAAG;QAAC;KAAS;IAEb,IAAI,CAAC,YAAY,CAAC,YAAY;QAC5B,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,MAAM,uBAAuB,CAAC,OAA+B;QAC3D,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;IAClD;IAEA,MAAM,eAAe,QAAQ,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,OAAO,OAAO,EAAE;IAC3E,MAAM,gBAAgB,QAAQ,MAAM,CAAC,CAAA,SAAU,OAAO,MAAM,KAAK,UAAU,MAAM;IACjF,MAAM,iBAAiB,QAAQ,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,OAAO,SAAS,EAAE;IAE/E,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;wCAAqB,OAAO;4CAAE,OAAO,SAAS,YAAY;wCAAC;kDACtE,SAAS,WAAW;;;;;;kDAEvB,6LAAC;wCAAK,WAAU;kDAAwB;;;;;;;;;;;;0CAE1C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;;4CAAwB;4CAC5B,CAAA,iBAAA,2BAAA,KAAM,SAAS,KAAI;4CAAO;;;;;;;kDAEtC,6LAAC,0KAAA,CAAA,aAAU;wCACT,YAAY;4CACV,UAAU;gDACR,WAAW;4CACb;wCACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOV,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ;gCACC;oCAAE,IAAI;oCAAY,MAAM;gCAAW;gCACnC;oCAAE,IAAI;oCAAW,MAAM;gCAAU;gCACjC;oCAAE,IAAI;oCAAY,MAAM;gCAAW;gCACnC;oCAAE,IAAI;oCAAW,MAAM;gCAAU;6BAClC,CAAC,GAAG,CAAC,CAAC,oBACL,6LAAC;oCAEC,SAAS,IAAM,aAAa,IAAI,EAAE;oCAClC,WAAW,AAAC,4CAIX,OAHC,cAAc,IAAI,EAAE,GAChB,kCACA;8CAGL,IAAI,IAAI;mCARJ,IAAI,EAAE;;;;;;;;;;;;;;;oBAelB,cAAc,4BACb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;4DAAqB,MAAK;4DAAe,SAAQ;;8EAC9D,6LAAC;oEAAK,GAAE;;;;;;8EACR,6LAAC;oEAAK,UAAS;oEAAU,GAAE;oEAAud,UAAS;;;;;;;;;;;;;;;;;;;;;;8DAIjgB,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAA6C;;;;;;0EAC3D,6LAAC;gEAAG,WAAU;;oEAAoC;oEAAE,aAAa,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAMvF,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;4DAAqB,MAAK;4DAAe,SAAQ;sEAC9D,cAAA,6LAAC;gEAAK,GAAE;;;;;;;;;;;;;;;;;;;;;8DAId,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAA6C;;;;;;0EAC3D,6LAAC;gEAAG,WAAU;0EAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAM3D,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;4DAAqB,MAAK;4DAAe,SAAQ;sEAC9D,cAAA,6LAAC;gEAAK,UAAS;gEAAU,GAAE;gEAA+f,UAAS;;;;;;;;;;;;;;;;;;;;;8DAIziB,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAA6C;;;;;;0EAC3D,6LAAC;gEAAG,WAAU;0EAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQ7D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAO,WAAU;;kEAChB,6LAAC;wDAAI,WAAU;kEAAoC;;;;;;kEACnD,6LAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;0DAEzC,6LAAC;gDAAO,WAAU;;kEAChB,6LAAC;wDAAI,WAAU;kEAAoC;;;;;;kEACnD,6LAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;0DAEzC,6LAAC;gDAAO,WAAU;;kEAChB,6LAAC;wDAAI,WAAU;kEAAoC;;;;;;kEACnD,6LAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;0DAEzC,6LAAC;gDAAO,WAAU;;kEAChB,6LAAC;wDAAI,WAAU;kEAAoC;;;;;;kEACnD,6LAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAQhD,cAAc,2BACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAG,WAAU;8CAAoC;;;;;;;;;;;0CAEpD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAM,WAAU;;sDACf,6LAAC;4CAAM,WAAU;sDACf,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,6LAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,6LAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,6LAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,6LAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;;;;;;;;;;;sDAIhC,6LAAC;4CAAM,WAAU;sDACd,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;;kFACC,6LAAC;wEAAI,WAAU;kFAAqC,OAAO,IAAI;;;;;;kFAC/D,6LAAC;wEAAI,WAAU;kFAAyB,OAAO,KAAK;;;;;;;;;;;;;;;;;sEAGxD,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAK,WAAW,AAAC,4DAIjB,OAHC,OAAO,MAAM,KAAK,WACd,gCACA;0EAEH,OAAO,MAAM;;;;;;;;;;;sEAGlB,6LAAC;4DAAG,WAAU;sEACX,OAAO,SAAS;;;;;;sEAEnB,6LAAC;4DAAG,WAAU;;gEAAoD;gEAC9D,OAAO,OAAO,CAAC,cAAc;;;;;;;sEAEjC,6LAAC;4DAAG,WAAU;sEACX,IAAI,KAAK,OAAO,UAAU,EAAE,kBAAkB;;;;;;sEAEjD,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAO,WAAU;0EAAoC;;;;;;;;;;;;mDA1BjD,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAuC7B,cAAc,4BACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CAEvD,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,6LAAC;wDACC,MAAK;wDACL,OAAO,SAAS,WAAW;wDAC3B,UAAU,CAAC,IAAM,qBAAqB,eAAe,EAAE,MAAM,CAAC,KAAK;wDACnE,WAAU;wDACV,aAAY;;;;;;;;;;;;0DAIhB,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,6LAAC;wDACC,MAAK;wDACL,OAAO,SAAS,IAAI;wDACpB,UAAU,CAAC,IAAM,qBAAqB,QAAQ,EAAE,MAAM,CAAC,KAAK;wDAC5D,WAAU;wDACV,aAAY;;;;;;;;;;;;0DAIhB,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEACC,MAAK;gEACL,OAAO,SAAS,YAAY;gEAC5B,UAAU,CAAC,IAAM,qBAAqB,gBAAgB,EAAE,MAAM,CAAC,KAAK;gEACpE,WAAU;;;;;;0EAEZ,6LAAC;gEACC,MAAK;gEACL,OAAO,SAAS,YAAY;gEAC5B,UAAU,CAAC,IAAM,qBAAqB,gBAAgB,EAAE,MAAM,CAAC,KAAK;gEACpE,WAAU;gEACV,aAAY;;;;;;;;;;;;;;;;;;0DAKlB,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEACC,MAAK;gEACL,OAAO,SAAS,cAAc;gEAC9B,UAAU,CAAC,IAAM,qBAAqB,kBAAkB,EAAE,MAAM,CAAC,KAAK;gEACtE,WAAU;;;;;;0EAEZ,6LAAC;gEACC,MAAK;gEACL,OAAO,SAAS,cAAc;gEAC9B,UAAU,CAAC,IAAM,qBAAqB,kBAAkB,EAAE,MAAM,CAAC,KAAK;gEACtE,WAAU;gEACV,aAAY;;;;;;;;;;;;;;;;;;0DAKlB,6LAAC;gDACC,WAAU;gDACV,SAAS,IAAM,MAAM;0DACtB;;;;;;;;;;;;kDAMH,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAGlD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;;gEACZ,SAAS,IAAI,kBACZ,6LAAC;oEAAI,KAAK,SAAS,IAAI;oEAAE,KAAI;oEAAO,WAAU;;;;;;8EAEhD,6LAAC;oEAAG,WAAU;oEAAoB,OAAO;wEAAE,OAAO,SAAS,YAAY;oEAAC;8EACrE,SAAS,WAAW;;;;;;;;;;;;;;;;;kEAK3B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEACC,WAAU;gEACV,OAAO;oEAAE,iBAAiB,SAAS,YAAY;gEAAC;0EACjD;;;;;;0EAGD,6LAAC;gEACC,WAAU;gEACV,OAAO;oEAAE,iBAAiB,SAAS,cAAc;gEAAC;0EACnD;;;;;;;;;;;;;;;;;;0DAOL,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;wDAAmB,OAAO;4DAAE,OAAO,SAAS,YAAY;wDAAC;kEAAG;;;;;;kEAG1E,6LAAC;wDAAE,WAAU;kEAA6B;;;;;;kEAG1C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEACC,WAAU;gEACV,OAAO;oEAAE,iBAAiB,SAAS,YAAY;gEAAC;;;;;;0EAElD,6LAAC;gEACC,WAAU;gEACV,OAAO;oEAAE,iBAAiB,SAAS,cAAc;gEAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAU/D,cAAc,2BACb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAyC;;;;;;kDAEvD,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;oDAA+B,MAAK;oDAAe,SAAQ;8DACxE,cAAA,6LAAC;wDAAK,UAAS;wDAAU,GAAE;wDAAoN,UAAS;;;;;;;;;;;8DAE1P,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAAsC;;;;;;sEACpD,6LAAC;4DAAE,WAAU;sEAA0B;;;;;;;;;;;;;;;;;;;;;;;kDAO7C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAO,WAAU;;kEAChB,6LAAC;wDAAI,WAAU;wDAAe,MAAK;wDAAe,SAAQ;kEACxD,cAAA,6LAAC;4DAAK,UAAS;4DAAU,GAAE;4DAA6J,UAAS;;;;;;;;;;;oDAC7L;;;;;;;0DAIR,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAO;;;;;;kEACpB,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;0EAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOZ,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAyC;;;;;;kDAEvD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAiC;;;;;;kEAC/C,6LAAC;wDAAE,WAAU;kEAAmC;;;;;;kEAChD,6LAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAGvC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAiC;;;;;;kEAC/C,6LAAC;wDAAE,WAAU;kEAAmC;;;;;;kEAChD,6LAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;kDAIzC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAiC;;;;;;0DAC/C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;wDAAuC,MAAK;wDAAe,SAAQ;kEAChF,cAAA,6LAAC;4DAAK,UAAS;4DAAU,GAAE;4DAAmI,UAAS;;;;;;;;;;;kEAEzK,6LAAC;kEAAE;;;;;;kEACH,6LAAC;wDAAE,WAAU;kEAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzC;GAjfwB;;QACiB,+JAAA,CAAA,UAAO;QAC/B,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}