{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_a8652ea1._.js", "server/edge/chunks/[root-of-the-server]__de110ef0._.js", "server/edge/chunks/edge-wrapper_0bcc2944.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(api|trpc))(.*)(\\\\.json)?[\\/#\\?]?$", "originalSource": "/(api|trpc)(.*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "bJ0NEv5HpdobvY5DawvXS6JS6QegkTDdNuWq6OerApw=", "__NEXT_PREVIEW_MODE_ID": "e7bc6f60baf7416b8d49fbfbe940f52c", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "a8433563982bd55c126277440b001f8b500964cea47a68e53e2a5c3a19b94dc3", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "3718534568d76614894ebf318aefbf42b3d6d6954bbda3337a777d85a50faa52"}}}, "sortedMiddleware": ["/"], "functions": {}}