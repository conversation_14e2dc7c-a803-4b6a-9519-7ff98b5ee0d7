{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_a8652ea1._.js", "server/edge/chunks/[root-of-the-server]__de110ef0._.js", "server/edge/chunks/edge-wrapper_0bcc2944.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*){(\\\\.json)}?", "originalSource": "/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/(api|trpc)(.*){(\\\\.json)}?", "originalSource": "/(api|trpc)(.*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "bJ0NEv5HpdobvY5DawvXS6JS6QegkTDdNuWq6OerApw=", "__NEXT_PREVIEW_MODE_ID": "e92244c7e7e2dda57f2c0f37146dc72f", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "ecb8651c5795f7d9c701554a0b257a56d708f11396fa3b1440a3968593a74312", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "de6c0675b2a49139f4cb6cf517f1c2259650269148c14553e18d064391668ad4"}}}, "instrumentation": null, "functions": {}}