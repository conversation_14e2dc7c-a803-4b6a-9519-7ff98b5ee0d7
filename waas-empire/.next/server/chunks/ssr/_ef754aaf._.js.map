{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/waas-empire/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 19, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/waas-empire/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/waas-empire/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 169, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/waas-empire/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 213, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/waas-empire/components/ui/navigation-menu.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as NavigationMenuPrimitive from \"@radix-ui/react-navigation-menu\"\nimport { cva } from \"class-variance-authority\"\nimport { ChevronDownIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction NavigationMenu({\n  className,\n  children,\n  viewport = true,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Root> & {\n  viewport?: boolean\n}) {\n  return (\n    <NavigationMenuPrimitive.Root\n      data-slot=\"navigation-menu\"\n      data-viewport={viewport}\n      className={cn(\n        \"group/navigation-menu relative flex max-w-max flex-1 items-center justify-center\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      {viewport && <NavigationMenuViewport />}\n    </NavigationMenuPrimitive.Root>\n  )\n}\n\nfunction NavigationMenuList({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.List>) {\n  return (\n    <NavigationMenuPrimitive.List\n      data-slot=\"navigation-menu-list\"\n      className={cn(\n        \"group flex flex-1 list-none items-center justify-center gap-1\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction NavigationMenuItem({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Item>) {\n  return (\n    <NavigationMenuPrimitive.Item\n      data-slot=\"navigation-menu-item\"\n      className={cn(\"relative\", className)}\n      {...props}\n    />\n  )\n}\n\nconst navigationMenuTriggerStyle = cva(\n  \"group inline-flex h-9 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground disabled:pointer-events-none disabled:opacity-50 data-[state=open]:hover:bg-accent data-[state=open]:text-accent-foreground data-[state=open]:focus:bg-accent data-[state=open]:bg-accent/50 focus-visible:ring-ring/50 outline-none transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1\"\n)\n\nfunction NavigationMenuTrigger({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Trigger>) {\n  return (\n    <NavigationMenuPrimitive.Trigger\n      data-slot=\"navigation-menu-trigger\"\n      className={cn(navigationMenuTriggerStyle(), \"group\", className)}\n      {...props}\n    >\n      {children}{\" \"}\n      <ChevronDownIcon\n        className=\"relative top-[1px] ml-1 size-3 transition duration-300 group-data-[state=open]:rotate-180\"\n        aria-hidden=\"true\"\n      />\n    </NavigationMenuPrimitive.Trigger>\n  )\n}\n\nfunction NavigationMenuContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Content>) {\n  return (\n    <NavigationMenuPrimitive.Content\n      data-slot=\"navigation-menu-content\"\n      className={cn(\n        \"data-[motion^=from-]:animate-in data-[motion^=to-]:animate-out data-[motion^=from-]:fade-in data-[motion^=to-]:fade-out data-[motion=from-end]:slide-in-from-right-52 data-[motion=from-start]:slide-in-from-left-52 data-[motion=to-end]:slide-out-to-right-52 data-[motion=to-start]:slide-out-to-left-52 top-0 left-0 w-full p-2 pr-2.5 md:absolute md:w-auto\",\n        \"group-data-[viewport=false]/navigation-menu:bg-popover group-data-[viewport=false]/navigation-menu:text-popover-foreground group-data-[viewport=false]/navigation-menu:data-[state=open]:animate-in group-data-[viewport=false]/navigation-menu:data-[state=closed]:animate-out group-data-[viewport=false]/navigation-menu:data-[state=closed]:zoom-out-95 group-data-[viewport=false]/navigation-menu:data-[state=open]:zoom-in-95 group-data-[viewport=false]/navigation-menu:data-[state=open]:fade-in-0 group-data-[viewport=false]/navigation-menu:data-[state=closed]:fade-out-0 group-data-[viewport=false]/navigation-menu:top-full group-data-[viewport=false]/navigation-menu:mt-1.5 group-data-[viewport=false]/navigation-menu:overflow-hidden group-data-[viewport=false]/navigation-menu:rounded-md group-data-[viewport=false]/navigation-menu:border group-data-[viewport=false]/navigation-menu:shadow group-data-[viewport=false]/navigation-menu:duration-200 **:data-[slot=navigation-menu-link]:focus:ring-0 **:data-[slot=navigation-menu-link]:focus:outline-none\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction NavigationMenuViewport({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Viewport>) {\n  return (\n    <div\n      className={cn(\n        \"absolute top-full left-0 isolate z-50 flex justify-center\"\n      )}\n    >\n      <NavigationMenuPrimitive.Viewport\n        data-slot=\"navigation-menu-viewport\"\n        className={cn(\n          \"origin-top-center bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-90 relative mt-1.5 h-[var(--radix-navigation-menu-viewport-height)] w-full overflow-hidden rounded-md border shadow md:w-[var(--radix-navigation-menu-viewport-width)]\",\n          className\n        )}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction NavigationMenuLink({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Link>) {\n  return (\n    <NavigationMenuPrimitive.Link\n      data-slot=\"navigation-menu-link\"\n      className={cn(\n        \"data-[active=true]:focus:bg-accent data-[active=true]:hover:bg-accent data-[active=true]:bg-accent/50 data-[active=true]:text-accent-foreground hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus-visible:ring-ring/50 [&_svg:not([class*='text-'])]:text-muted-foreground flex flex-col gap-1 rounded-sm p-2 text-sm transition-all outline-none focus-visible:ring-[3px] focus-visible:outline-1 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction NavigationMenuIndicator({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Indicator>) {\n  return (\n    <NavigationMenuPrimitive.Indicator\n      data-slot=\"navigation-menu-indicator\"\n      className={cn(\n        \"data-[state=visible]:animate-in data-[state=hidden]:animate-out data-[state=hidden]:fade-out data-[state=visible]:fade-in top-full z-[1] flex h-1.5 items-end justify-center overflow-hidden\",\n        className\n      )}\n      {...props}\n    >\n      <div className=\"bg-border relative top-[60%] h-2 w-2 rotate-45 rounded-tl-sm shadow-md\" />\n    </NavigationMenuPrimitive.Indicator>\n  )\n}\n\nexport {\n  NavigationMenu,\n  NavigationMenuList,\n  NavigationMenuItem,\n  NavigationMenuContent,\n  NavigationMenuTrigger,\n  NavigationMenuLink,\n  NavigationMenuIndicator,\n  NavigationMenuViewport,\n  navigationMenuTriggerStyle,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AACA;AACA;AACA;AAEA;;;;;;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,QAAQ,EACR,WAAW,IAAI,EACf,GAAG,OAGJ;IACC,qBACE,8OAAC,8KAAA,CAAA,OAA4B;QAC3B,aAAU;QACV,iBAAe;QACf,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,oFACA;QAED,GAAG,KAAK;;YAER;YACA,0BAAY,8OAAC;;;;;;;;;;;AAGpB;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,8OAAC,8KAAA,CAAA,OAA4B;QAC3B,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,8OAAC,8KAAA,CAAA,OAA4B;QAC3B,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;;;;;;AAGf;AAEA,MAAM,6BAA6B,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACnC;AAGF,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,8OAAC,8KAAA,CAAA,UAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B,SAAS;QACpD,GAAG,KAAK;;YAER;YAAU;0BACX,8OAAC,wNAAA,CAAA,kBAAe;gBACd,WAAU;gBACV,eAAY;;;;;;;;;;;;AAIpB;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,8KAAA,CAAA,UAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,oWACA,6hCACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV;kBAGF,cAAA,8OAAC,8KAAA,CAAA,WAAgC;YAC/B,aAAU;YACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,sVACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,8OAAC,8KAAA,CAAA,OAA4B;QAC3B,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,ydACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,wBAAwB,EAC/B,SAAS,EACT,GAAG,OAC4D;IAC/D,qBACE,8OAAC,8KAAA,CAAA,YAAiC;QAChC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,gMACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC;YAAI,WAAU;;;;;;;;;;;AAGrB", "debugId": null}}, {"offset": {"line": 363, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/waas-empire/components/navigation.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { useUser, UserButton } from '@clerk/nextjs';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport {\n  NavigationMenu,\n  NavigationMenuContent,\n  NavigationMenuItem,\n  NavigationMenuLink,\n  NavigationMenuList,\n  NavigationMenuTrigger,\n} from '@/components/ui/navigation-menu';\n\ninterface NavigationProps {\n  variant?: 'landing' | 'dashboard';\n  showUserButton?: boolean;\n}\n\nexport function Navigation({ variant = 'landing', showUserButton = true }: NavigationProps) {\n  const { isSignedIn, isLoaded } = useUser();\n\n  const Logo = () => (\n    <Link href=\"/\" className=\"flex items-center space-x-2\">\n      <div className=\"w-8 h-8 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center\">\n        <span className=\"text-white font-bold text-sm\">W</span>\n      </div>\n      <span className=\"text-xl font-bold text-gray-900\">WaaS Empire</span>\n    </Link>\n  );\n\n  if (variant === 'dashboard') {\n    return (\n      <nav className=\"border-b bg-white shadow-sm\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <Logo />\n            \n            <NavigationMenu>\n              <NavigationMenuList>\n                <NavigationMenuItem>\n                  <NavigationMenuTrigger>Dashboard</NavigationMenuTrigger>\n                  <NavigationMenuContent>\n                    <div className=\"grid gap-3 p-6 w-[400px]\">\n                      <NavigationMenuLink asChild>\n                        <Link\n                          href=\"/agency/dashboard\"\n                          className=\"block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground\"\n                        >\n                          <div className=\"text-sm font-medium leading-none\">Overview</div>\n                          <p className=\"line-clamp-2 text-sm leading-snug text-muted-foreground\">\n                            View your agency metrics and performance\n                          </p>\n                        </Link>\n                      </NavigationMenuLink>\n                      <NavigationMenuLink asChild>\n                        <Link\n                          href=\"/agency/dashboard?tab=clients\"\n                          className=\"block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground\"\n                        >\n                          <div className=\"text-sm font-medium leading-none\">Clients</div>\n                          <p className=\"line-clamp-2 text-sm leading-snug text-muted-foreground\">\n                            Manage your client accounts and workflows\n                          </p>\n                        </Link>\n                      </NavigationMenuLink>\n                      <NavigationMenuLink asChild>\n                        <Link\n                          href=\"/agency/dashboard?tab=branding\"\n                          className=\"block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground\"\n                        >\n                          <div className=\"text-sm font-medium leading-none\">Branding</div>\n                          <p className=\"line-clamp-2 text-sm leading-snug text-muted-foreground\">\n                            Customize your white-label appearance\n                          </p>\n                        </Link>\n                      </NavigationMenuLink>\n                    </div>\n                  </NavigationMenuContent>\n                </NavigationMenuItem>\n                \n                <NavigationMenuItem>\n                  <NavigationMenuLink asChild>\n                    <Link\n                      href=\"/marketplace\"\n                      className=\"group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50\"\n                    >\n                      Marketplace\n                      <Badge variant=\"secondary\" className=\"ml-2\">\n                        New\n                      </Badge>\n                    </Link>\n                  </NavigationMenuLink>\n                </NavigationMenuItem>\n              </NavigationMenuList>\n            </NavigationMenu>\n\n            <div className=\"flex items-center space-x-4\">\n              {isLoaded && showUserButton && <UserButton />}\n            </div>\n          </div>\n        </div>\n      </nav>\n    );\n  }\n\n  // Landing page navigation\n  return (\n    <nav className=\"border-b bg-white/80 backdrop-blur-sm sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          <Logo />\n          \n          <div className=\"hidden md:flex items-center space-x-8\">\n            <NavigationMenu>\n              <NavigationMenuList>\n                <NavigationMenuItem>\n                  <NavigationMenuTrigger>Features</NavigationMenuTrigger>\n                  <NavigationMenuContent>\n                    <div className=\"grid gap-3 p-6 w-[500px] grid-cols-2\">\n                      <div className=\"space-y-3\">\n                        <h4 className=\"text-sm font-medium leading-none\">Platform</h4>\n                        <NavigationMenuLink asChild>\n                          <a\n                            href=\"#features\"\n                            className=\"block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground\"\n                          >\n                            <div className=\"text-sm font-medium leading-none\">White-Label</div>\n                            <p className=\"line-clamp-2 text-sm leading-snug text-muted-foreground\">\n                              Complete branding customization\n                            </p>\n                          </a>\n                        </NavigationMenuLink>\n                        <NavigationMenuLink asChild>\n                          <a\n                            href=\"#features\"\n                            className=\"block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground\"\n                          >\n                            <div className=\"text-sm font-medium leading-none\">Auto-Deploy</div>\n                            <p className=\"line-clamp-2 text-sm leading-snug text-muted-foreground\">\n                              One-click n8n deployment\n                            </p>\n                          </a>\n                        </NavigationMenuLink>\n                      </div>\n                      <div className=\"space-y-3\">\n                        <h4 className=\"text-sm font-medium leading-none\">Revenue</h4>\n                        <NavigationMenuLink asChild>\n                          <a\n                            href=\"#features\"\n                            className=\"block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground\"\n                          >\n                            <div className=\"text-sm font-medium leading-none\">Marketplace</div>\n                            <p className=\"line-clamp-2 text-sm leading-snug text-muted-foreground\">\n                              50% revenue sharing\n                            </p>\n                          </a>\n                        </NavigationMenuLink>\n                        <NavigationMenuLink asChild>\n                          <a\n                            href=\"#features\"\n                            className=\"block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground\"\n                          >\n                            <div className=\"text-sm font-medium leading-none\">Stripe Connect</div>\n                            <p className=\"line-clamp-2 text-sm leading-snug text-muted-foreground\">\n                              Automated payments\n                            </p>\n                          </a>\n                        </NavigationMenuLink>\n                      </div>\n                    </div>\n                  </NavigationMenuContent>\n                </NavigationMenuItem>\n                \n                <NavigationMenuItem>\n                  <NavigationMenuLink asChild>\n                    <Link\n                      href=\"/marketplace\"\n                      className=\"group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50\"\n                    >\n                      Marketplace\n                    </Link>\n                  </NavigationMenuLink>\n                </NavigationMenuItem>\n                \n                <NavigationMenuItem>\n                  <NavigationMenuLink asChild>\n                    <a\n                      href=\"#pricing\"\n                      className=\"group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50\"\n                    >\n                      Pricing\n                    </a>\n                  </NavigationMenuLink>\n                </NavigationMenuItem>\n              </NavigationMenuList>\n            </NavigationMenu>\n          </div>\n          \n          <div className=\"flex items-center space-x-4\">\n            {isLoaded && (\n              <>\n                {isSignedIn ? (\n                  <div className=\"flex items-center space-x-2\">\n                    <Link href=\"/agency/dashboard\">\n                      <Button variant=\"outline\">Dashboard</Button>\n                    </Link>\n                    {showUserButton && <UserButton />}\n                  </div>\n                ) : (\n                  <div className=\"flex items-center space-x-2\">\n                    <Link href=\"/waitlist\">\n                      <Button variant=\"outline\">Join Waitlist</Button>\n                    </Link>\n                    <Link href=\"/waitlist\">\n                      <Button>Get Started</Button>\n                    </Link>\n                  </div>\n                )}\n              </>\n            )}\n          </div>\n        </div>\n      </div>\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AANA;;;;;;;AAoBO,SAAS,WAAW,EAAE,UAAU,SAAS,EAAE,iBAAiB,IAAI,EAAmB;IACxF,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,UAAO,AAAD;IAEvC,MAAM,OAAO,kBACX,8OAAC,4JAAA,CAAA,UAAI;YAAC,MAAK;YAAI,WAAU;;8BACvB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAK,WAAU;kCAA+B;;;;;;;;;;;8BAEjD,8OAAC;oBAAK,WAAU;8BAAkC;;;;;;;;;;;;IAItD,IAAI,YAAY,aAAa;QAC3B,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;;;;sCAED,8OAAC,uIAAA,CAAA,iBAAc;sCACb,cAAA,8OAAC,uIAAA,CAAA,qBAAkB;;kDACjB,8OAAC,uIAAA,CAAA,qBAAkB;;0DACjB,8OAAC,uIAAA,CAAA,wBAAqB;0DAAC;;;;;;0DACvB,8OAAC,uIAAA,CAAA,wBAAqB;0DACpB,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,uIAAA,CAAA,qBAAkB;4DAAC,OAAO;sEACzB,cAAA,8OAAC,4JAAA,CAAA,UAAI;gEACH,MAAK;gEACL,WAAU;;kFAEV,8OAAC;wEAAI,WAAU;kFAAmC;;;;;;kFAClD,8OAAC;wEAAE,WAAU;kFAA0D;;;;;;;;;;;;;;;;;sEAK3E,8OAAC,uIAAA,CAAA,qBAAkB;4DAAC,OAAO;sEACzB,cAAA,8OAAC,4JAAA,CAAA,UAAI;gEACH,MAAK;gEACL,WAAU;;kFAEV,8OAAC;wEAAI,WAAU;kFAAmC;;;;;;kFAClD,8OAAC;wEAAE,WAAU;kFAA0D;;;;;;;;;;;;;;;;;sEAK3E,8OAAC,uIAAA,CAAA,qBAAkB;4DAAC,OAAO;sEACzB,cAAA,8OAAC,4JAAA,CAAA,UAAI;gEACH,MAAK;gEACL,WAAU;;kFAEV,8OAAC;wEAAI,WAAU;kFAAmC;;;;;;kFAClD,8OAAC;wEAAE,WAAU;kFAA0D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDASjF,8OAAC,uIAAA,CAAA,qBAAkB;kDACjB,cAAA,8OAAC,uIAAA,CAAA,qBAAkB;4CAAC,OAAO;sDACzB,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;oDACX;kEAEC,8OAAC,0HAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAY,WAAU;kEAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAStD,8OAAC;4BAAI,WAAU;sCACZ,YAAY,gCAAkB,8OAAC,uKAAA,CAAA,aAAU;;;;;;;;;;;;;;;;;;;;;;;;;;IAMtD;IAEA,0BAA0B;IAC1B,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;;;;kCAED,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,uIAAA,CAAA,iBAAc;sCACb,cAAA,8OAAC,uIAAA,CAAA,qBAAkB;;kDACjB,8OAAC,uIAAA,CAAA,qBAAkB;;0DACjB,8OAAC,uIAAA,CAAA,wBAAqB;0DAAC;;;;;;0DACvB,8OAAC,uIAAA,CAAA,wBAAqB;0DACpB,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAAmC;;;;;;8EACjD,8OAAC,uIAAA,CAAA,qBAAkB;oEAAC,OAAO;8EACzB,cAAA,8OAAC;wEACC,MAAK;wEACL,WAAU;;0FAEV,8OAAC;gFAAI,WAAU;0FAAmC;;;;;;0FAClD,8OAAC;gFAAE,WAAU;0FAA0D;;;;;;;;;;;;;;;;;8EAK3E,8OAAC,uIAAA,CAAA,qBAAkB;oEAAC,OAAO;8EACzB,cAAA,8OAAC;wEACC,MAAK;wEACL,WAAU;;0FAEV,8OAAC;gFAAI,WAAU;0FAAmC;;;;;;0FAClD,8OAAC;gFAAE,WAAU;0FAA0D;;;;;;;;;;;;;;;;;;;;;;;sEAM7E,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAAmC;;;;;;8EACjD,8OAAC,uIAAA,CAAA,qBAAkB;oEAAC,OAAO;8EACzB,cAAA,8OAAC;wEACC,MAAK;wEACL,WAAU;;0FAEV,8OAAC;gFAAI,WAAU;0FAAmC;;;;;;0FAClD,8OAAC;gFAAE,WAAU;0FAA0D;;;;;;;;;;;;;;;;;8EAK3E,8OAAC,uIAAA,CAAA,qBAAkB;oEAAC,OAAO;8EACzB,cAAA,8OAAC;wEACC,MAAK;wEACL,WAAU;;0FAEV,8OAAC;gFAAI,WAAU;0FAAmC;;;;;;0FAClD,8OAAC;gFAAE,WAAU;0FAA0D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAUnF,8OAAC,uIAAA,CAAA,qBAAkB;kDACjB,cAAA,8OAAC,uIAAA,CAAA,qBAAkB;4CAAC,OAAO;sDACzB,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;;;;;kDAML,8OAAC,uIAAA,CAAA,qBAAkB;kDACjB,cAAA,8OAAC,uIAAA,CAAA,qBAAkB;4CAAC,OAAO;sDACzB,cAAA,8OAAC;gDACC,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASX,8OAAC;wBAAI,WAAU;kCACZ,0BACC;sCACG,2BACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,2HAAA,CAAA,SAAM;4CAAC,SAAQ;sDAAU;;;;;;;;;;;oCAE3B,gCAAkB,8OAAC,uKAAA,CAAA,aAAU;;;;;;;;;;qDAGhC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,2HAAA,CAAA,SAAM;4CAAC,SAAQ;sDAAU;;;;;;;;;;;kDAE5B,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,2HAAA,CAAA,SAAM;sDAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW9B", "debugId": null}}, {"offset": {"line": 1003, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/waas-empire/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Badge } from '@/components/ui/badge';\nimport { Navigation } from '@/components/navigation';\n\nexport default function LandingPage() {\n\n  const features = [\n    {\n      title: \"White-Label Solution\",\n      description: \"Complete branding customization for your agency with dynamic theming and logo support.\",\n      icon: \"🎨\",\n      badge: \"Customizable\"\n    },\n    {\n      title: \"Workflow Marketplace\",\n      description: \"Access thousands of pre-built automation workflows from our curated marketplace.\",\n      icon: \"🛒\",\n      badge: \"1000+ Workflows\"\n    },\n    {\n      title: \"Revenue Sharing\",\n      description: \"Earn 50% revenue share on marketplace sales with automatic Stripe Connect integration.\",\n      icon: \"💰\",\n      badge: \"50% Share\"\n    },\n    {\n      title: \"Auto-Deploy\",\n      description: \"One-click deployment to n8n instances with self-healing error handling.\",\n      icon: \"🚀\",\n      badge: \"Instant Deploy\"\n    },\n    {\n      title: \"Client Management\",\n      description: \"Comprehensive dashboard to manage client accounts and track performance metrics.\",\n      icon: \"👥\",\n      badge: \"Multi-Client\"\n    },\n    {\n      title: \"Stripe Connect\",\n      description: \"Built-in payment processing with 20% platform fee and automated billing.\",\n      icon: \"💳\",\n      badge: \"Secure Payments\"\n    }\n  ];\n\n  const stats = [\n    { label: \"Active Agencies\", value: \"500+\" },\n    { label: \"Workflows Deployed\", value: \"10K+\" },\n    { label: \"Revenue Generated\", value: \"$2M+\" },\n    { label: \"Client Satisfaction\", value: \"99%\" }\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 to-blue-50\">\n      <Navigation variant=\"landing\" />\n\n      {/* Hero Section */}\n      <section className=\"py-20 px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-7xl mx-auto text-center\">\n          <div className=\"mb-8\">\n            <Badge variant=\"secondary\" className=\"mb-4\">\n              🚀 Now in Beta - Join the Revolution\n            </Badge>\n            <h1 className=\"text-4xl md:text-6xl font-bold text-gray-900 mb-6\">\n              The Ultimate{' '}\n              <span className=\"bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\">\n                White-Label\n              </span>\n              <br />\n              Workflow Platform\n            </h1>\n            <p className=\"text-xl text-gray-600 mb-8 max-w-3xl mx-auto\">\n              Empower your agency with a complete workflow automation platform.\n              Deploy, customize, and monetize automation workflows with zero technical overhead.\n            </p>\n          </div>\n\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center mb-12\">\n            <Link href=\"/waitlist\">\n              <Button size=\"lg\" className=\"text-lg px-8 py-6\">\n                Start Your Agency Today\n              </Button>\n            </Link>\n            <Link href=\"/marketplace\">\n              <Button variant=\"outline\" size=\"lg\" className=\"text-lg px-8 py-6\">\n                Browse Marketplace\n              </Button>\n            </Link>\n          </div>\n\n          {/* Stats */}\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto\">\n            {stats.map((stat, index) => (\n              <div key={index} className=\"text-center\">\n                <div className=\"text-3xl font-bold text-blue-600 mb-2\">{stat.value}</div>\n                <div className=\"text-gray-600\">{stat.label}</div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Features Section */}\n      <section className=\"py-20 px-4 sm:px-6 lg:px-8 bg-white\">\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n              Everything You Need to Scale\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n              Built for agencies who want to offer workflow automation without the technical complexity.\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {features.map((feature, index) => (\n              <Card key={index} className=\"hover:shadow-lg transition-shadow border-0 shadow-md\">\n                <CardHeader>\n                  <div className=\"flex items-center justify-between mb-2\">\n                    <div className=\"text-3xl\">{feature.icon}</div>\n                    <Badge variant=\"secondary\">{feature.badge}</Badge>\n                  </div>\n                  <CardTitle className=\"text-xl\">{feature.title}</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <CardDescription className=\"text-gray-600\">\n                    {feature.description}\n                  </CardDescription>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-blue-600 to-purple-600\">\n        <div className=\"max-w-4xl mx-auto text-center\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-white mb-6\">\n            Ready to Transform Your Agency?\n          </h2>\n          <p className=\"text-xl text-blue-100 mb-8\">\n            Join hundreds of agencies already using WaaS Empire to deliver world-class automation solutions.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Link href=\"/waitlist\">\n              <Button size=\"lg\" variant=\"secondary\" className=\"text-lg px-8 py-6\">\n                Join the Waitlist\n              </Button>\n            </Link>\n            <Button size=\"lg\" variant=\"outline\" className=\"text-lg px-8 py-6 text-white border-white hover:bg-white hover:text-blue-600\">\n              Schedule Demo\n            </Button>\n          </div>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"bg-gray-900 text-white py-12 px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n            <div className=\"col-span-1 md:col-span-2\">\n              <div className=\"flex items-center space-x-2 mb-4\">\n                <div className=\"w-8 h-8 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white font-bold text-sm\">W</span>\n                </div>\n                <span className=\"text-xl font-bold\">WaaS Empire</span>\n              </div>\n              <p className=\"text-gray-400 mb-4 max-w-md\">\n                The ultimate white-label workflow automation platform for agencies.\n                Scale your business with zero technical overhead.\n              </p>\n            </div>\n\n            <div>\n              <h3 className=\"font-semibold mb-4\">Product</h3>\n              <ul className=\"space-y-2 text-gray-400\">\n                <li><Link href=\"/marketplace\" className=\"hover:text-white\">Marketplace</Link></li>\n                <li><Link href=\"/waitlist\" className=\"hover:text-white\">Pricing</Link></li>\n                <li><Link href=\"/waitlist\" className=\"hover:text-white\">Documentation</Link></li>\n              </ul>\n            </div>\n\n            <div>\n              <h3 className=\"font-semibold mb-4\">Company</h3>\n              <ul className=\"space-y-2 text-gray-400\">\n                <li><Link href=\"/waitlist\" className=\"hover:text-white\">About</Link></li>\n                <li><Link href=\"/waitlist\" className=\"hover:text-white\">Contact</Link></li>\n                <li><Link href=\"/waitlist\" className=\"hover:text-white\">Support</Link></li>\n              </ul>\n            </div>\n          </div>\n\n          <div className=\"border-t border-gray-800 mt-8 pt-8 text-center text-gray-400\">\n            <p>&copy; 2024 WaaS Empire. All rights reserved.</p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQe,SAAS;IAEtB,MAAM,WAAW;QACf;YACE,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;QACT;QACA;YACE,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;QACT;QACA;YACE,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;QACT;QACA;YACE,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;QACT;QACA;YACE,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;QACT;QACA;YACE,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;QACT;KACD;IAED,MAAM,QAAQ;QACZ;YAAE,OAAO;YAAmB,OAAO;QAAO;QAC1C;YAAE,OAAO;YAAsB,OAAO;QAAO;QAC7C;YAAE,OAAO;YAAqB,OAAO;QAAO;QAC5C;YAAE,OAAO;YAAuB,OAAO;QAAM;KAC9C;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,yHAAA,CAAA,aAAU;gBAAC,SAAQ;;;;;;0BAGpB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0HAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAY,WAAU;8CAAO;;;;;;8CAG5C,8OAAC;oCAAG,WAAU;;wCAAoD;wCACnD;sDACb,8OAAC;4CAAK,WAAU;sDAA6E;;;;;;sDAG7F,8OAAC;;;;;wCAAK;;;;;;;8CAGR,8OAAC;oCAAE,WAAU;8CAA+C;;;;;;;;;;;;sCAM9D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,8OAAC,2HAAA,CAAA,SAAM;wCAAC,MAAK;wCAAK,WAAU;kDAAoB;;;;;;;;;;;8CAIlD,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,8OAAC,2HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,WAAU;kDAAoB;;;;;;;;;;;;;;;;;sCAOtE,8OAAC;4BAAI,WAAU;sCACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;oCAAgB,WAAU;;sDACzB,8OAAC;4CAAI,WAAU;sDAAyC,KAAK,KAAK;;;;;;sDAClE,8OAAC;4CAAI,WAAU;sDAAiB,KAAK,KAAK;;;;;;;mCAFlC;;;;;;;;;;;;;;;;;;;;;0BAUlB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,8OAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAKzD,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,yHAAA,CAAA,OAAI;oCAAa,WAAU;;sDAC1B,8OAAC,yHAAA,CAAA,aAAU;;8DACT,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAY,QAAQ,IAAI;;;;;;sEACvC,8OAAC,0HAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAa,QAAQ,KAAK;;;;;;;;;;;;8DAE3C,8OAAC,yHAAA,CAAA,YAAS;oDAAC,WAAU;8DAAW,QAAQ,KAAK;;;;;;;;;;;;sDAE/C,8OAAC,yHAAA,CAAA,cAAW;sDACV,cAAA,8OAAC,yHAAA,CAAA,kBAAe;gDAAC,WAAU;0DACxB,QAAQ,WAAW;;;;;;;;;;;;mCAVf;;;;;;;;;;;;;;;;;;;;;0BAoBnB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAiD;;;;;;sCAG/D,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAG1C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,8OAAC,2HAAA,CAAA,SAAM;wCAAC,MAAK;wCAAK,SAAQ;wCAAY,WAAU;kDAAoB;;;;;;;;;;;8CAItE,8OAAC,2HAAA,CAAA,SAAM;oCAAC,MAAK;oCAAK,SAAQ;oCAAU,WAAU;8CAA+E;;;;;;;;;;;;;;;;;;;;;;;0BAQnI,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAA+B;;;;;;;;;;;8DAEjD,8OAAC;oDAAK,WAAU;8DAAoB;;;;;;;;;;;;sDAEtC,8OAAC;4CAAE,WAAU;sDAA8B;;;;;;;;;;;;8CAM7C,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAe,WAAU;kEAAmB;;;;;;;;;;;8DAC3D,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAY,WAAU;kEAAmB;;;;;;;;;;;8DACxD,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAY,WAAU;kEAAmB;;;;;;;;;;;;;;;;;;;;;;;8CAI5D,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAY,WAAU;kEAAmB;;;;;;;;;;;8DACxD,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAY,WAAU;kEAAmB;;;;;;;;;;;8DACxD,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAY,WAAU;kEAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAK9D,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;0CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMf", "debugId": null}}]}