{"node": {"7f2133385cde8e5300d6ee729e0ab2a518ca4b82e9": {"workers": {"app/agency/dashboard/page": {"moduleId": "[project]/.next-internal/server/app/agency/dashboard/page/actions.js { ACTIONS_MODULE0 => \"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/agency/dashboard/page": "action-browser"}}, "7f2d0739646ae460fc10532dc4f03a1021e9deaf3b": {"workers": {"app/agency/dashboard/page": {"moduleId": "[project]/.next-internal/server/app/agency/dashboard/page/actions.js { ACTIONS_MODULE0 => \"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/agency/dashboard/page": "action-browser"}}, "7fc00d7f35d89853f0cd5262f9173f0b9eaaffc1e0": {"workers": {"app/agency/dashboard/page": {"moduleId": "[project]/.next-internal/server/app/agency/dashboard/page/actions.js { ACTIONS_MODULE0 => \"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/agency/dashboard/page": "action-browser"}}, "7f58c3b3ae67b555f4b2165ceab13eb0344d04f51a": {"workers": {"app/agency/dashboard/page": {"moduleId": "[project]/.next-internal/server/app/agency/dashboard/page/actions.js { ACTIONS_MODULE0 => \"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/agency/dashboard/page": "action-browser"}}}, "edge": {}}