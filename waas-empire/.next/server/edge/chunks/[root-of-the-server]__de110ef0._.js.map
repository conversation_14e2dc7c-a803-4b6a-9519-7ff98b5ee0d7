{"version": 3, "sources": [], "sections": [{"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/middleware.ts"], "sourcesContent": ["import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server';\n\nconst isProtectedRoute = createRouteMatcher([\n  '/agency(.*)',\n  '/marketplace(.*)',\n  '/api/deploy(.*)',\n  '/api/onboard(.*)',\n]);\n\nexport default clerkMiddleware((auth, req) => {\n  if (isProtectedRoute(req)) auth().protect();\n});\n\nexport const config = {\n  matcher: [\n    // Skip Next.js internals and all static files, unless found in search params\n    '/((?!_next|[^?]*\\\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',\n    // Always run for API routes\n    '/(api|trpc)(.*)',\n  ],\n};\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAEA,MAAM,mBAAmB,CAAA,GAAA,kLAAA,CAAA,qBAAkB,AAAD,EAAE;IAC1C;IACA;IACA;IACA;CACD;uCAEc,CAAA,GAAA,qLAAA,CAAA,kBAAe,AAAD,EAAE,CAAC,MAAM;IACpC,IAAI,iBAAiB,MAAM,OAAO,OAAO;AAC3C;AAEO,MAAM,SAAS;IACpB,SAAS;QACP,6EAA6E;QAC7E;QACA,4BAA4B;QAC5B;KACD;AACH"}}]}