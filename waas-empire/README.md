# WaaS Empire - Workflow as a Service Platform

A comprehensive white-label SaaS platform that enables agencies to offer workflow automation services to their clients with built-in revenue sharing capabilities.

## 🚀 Features

### Core Platform
- **White-Label Solution**: Complete branding customization with dynamic theming
- **Clerk Authentication**: Secure user management and authentication
- **Responsive Design**: Mobile-first design with Tailwind CSS v4
- **shadcn/ui Components**: Modern, accessible UI components

### Agency Dashboard
- **Multi-tab Interface**: Overview, Clients, Branding, and Billing management
- **Dynamic Branding**: Live preview of white-label customizations
- **Client Management**: Track client accounts and workflow deployments
- **Revenue Analytics**: Monitor earnings and platform performance

### Workflow Marketplace
- **Search & Filter**: Find workflows by category, tags, or keywords
- **One-Click Deploy**: Automated n8n workflow deployment
- **Rating System**: Community-driven workflow ratings
- **Revenue Sharing**: 50% to creators, 30% to agencies, 20% platform fee

### n8n Integration
- **Auto-Deploy API**: Seamless workflow deployment to n8n instances
- **Error Handling**: Self-healing deployment with retry logic
- **Fallback Methods**: Multiple deployment strategies for reliability

### Stripe Connect Integration
- **Express Accounts**: Easy onboarding for agencies
- **Revenue Sharing**: Automated payment distribution
- **Platform Fees**: 20% platform fee with transparent pricing

## 🛠 Tech Stack

- **Framework**: Next.js 15.4.4 with App Router
- **Language**: TypeScript with strict mode
- **Styling**: Tailwind CSS v4 + shadcn/ui
- **Authentication**: Clerk
- **Payments**: Stripe Connect
- **Deployment**: Vercel-ready
- **APIs**: n8n, Google Sheets

## 📁 Project Structure

```
waas-empire/
├── app/
│   ├── page.tsx                 # Landing page
│   ├── waitlist/               # Waitlist signup
│   ├── agency/dashboard/       # Agency dashboard
│   ├── marketplace/            # Workflow marketplace
│   └── api/
│       ├── deploy/             # n8n deployment API
│       └── onboard/            # Stripe Connect onboarding
├── components/
│   ├── ui/                     # shadcn/ui components
│   ├── navigation.tsx          # Reusable navigation
│   ├── loading.tsx             # Loading components
│   └── workflow-card.tsx       # Marketplace workflow cards
├── lib/
│   └── utils.ts                # Utility functions
└── middleware.ts               # Clerk route protection
```

## 🔧 Environment Variables

Create a `.env.local` file with the following variables:

```env
# Clerk Authentication
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_b3B0aW1hbC1oYWdmaXNoLTMwLmNsZXJrLmFjY291bnRzLmRldiQ
CLERK_SECRET_KEY=sk_test_sqFqyPoRs3AblGMtoTrKJ5uN37AqQunJ9Q08gGmLSR

# Stripe Connect
STRIPE_SECRET_KEY=your_stripe_secret_key
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=your_webhook_secret

# Google Sheets API
GOOGLE_SHEETS_API_KEY=your_google_sheets_api_key
GOOGLE_SHEETS_SPREADSHEET_ID=your_spreadsheet_id

# n8n Configuration
N8N_API_KEY=your_n8n_api_key
N8N_API_BASE_URL=https://your-n8n-instance.com

# Platform Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
PLATFORM_FEE_PERCENTAGE=20
MARKETPLACE_REVENUE_SHARE=50
```

## 🚀 Getting Started

1. **Clone and Install**
   ```bash
   cd waas-empire
   npm install
   ```

2. **Set up Environment**
   - Copy `.env.example` to `.env.local`
   - Fill in your API keys and configuration

3. **Run Development Server**
   ```bash
   npm run dev
   ```

4. **Open in Browser**
   Navigate to `http://localhost:3000`

## 🧪 Testing Guide

### 1. Authentication Flow
- Visit the landing page
- Click "Join Waitlist" to test Clerk signup
- Complete the authentication process
- Verify redirect to agency dashboard

### 2. Agency Dashboard
- Test all tabs: Overview, Clients, Branding, Billing
- Modify branding settings and verify live preview
- Check responsive design on mobile devices

### 3. Marketplace
- Browse workflows with search and filter
- Test workflow deployment (requires n8n setup)
- Verify payment processing flow

### 4. API Endpoints
- Test `/api/deploy` with workflow JSON
- Test `/api/onboard` for Stripe Connect setup
- Verify error handling and retry logic

## 🌐 Deployment

### Vercel Deployment
1. Connect your GitHub repository to Vercel
2. Add environment variables in Vercel dashboard
3. Deploy with automatic builds on push

### Environment Setup
- Ensure all API keys are properly configured
- Set up Stripe webhooks for production
- Configure n8n instance for workflow deployment

## 💰 Revenue Model

- **Platform Fee**: 20% on all transactions
- **Creator Share**: 50% to workflow creators
- **Agency Share**: 30% to deploying agencies
- **Automated Distribution**: Via Stripe Connect

## 🔒 Security Features

- **Route Protection**: Clerk middleware for authenticated routes
- **API Authentication**: User verification on all API endpoints
- **Secure Payments**: Stripe Connect for PCI compliance
- **Environment Variables**: Sensitive data protection

## 📊 Key Metrics

Track these metrics for platform success:
- User signups and retention
- Workflow deployments
- Revenue per agency
- Marketplace transaction volume
- Platform fee collection

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is proprietary software for WaaS Empire.

## 🆘 Support

For technical support or questions:
- Check the documentation
- Review error logs in development
- Contact the development team

---

Built with ❤️ for the automation community
