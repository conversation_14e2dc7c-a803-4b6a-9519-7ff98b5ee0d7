{"name": "waas-empire", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@clerk/nextjs": "^6.27.1", "@stripe/stripe-js": "^7.6.1", "axios": "^1.11.0", "googleapis": "^154.0.0", "next": "15.4.4", "react": "19.1.0", "react-dom": "19.1.0", "stripe": "^18.3.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20.19.9", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "eslint": "^9", "eslint-config-next": "15.4.4", "tailwindcss": "^4", "typescript": "^5"}}