import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import <PERSON><PERSON> from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-12-18.acacia',
});

interface OnboardRequest {
  businessType?: 'individual' | 'company';
  country?: string;
  email?: string;
  businessName?: string;
}

export async function POST(request: NextRequest) {
  try {
    // Authenticate the user
    const { userId } = auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body: OnboardRequest = await request.json();
    const { 
      businessType = 'company', 
      country = 'US', 
      email,
      businessName 
    } = body;

    // Create Stripe Express account
    const account = await stripe.accounts.create({
      type: 'express',
      country: country,
      email: email,
      business_type: businessType,
      company: businessName ? {
        name: businessName,
      } : undefined,
      capabilities: {
        card_payments: { requested: true },
        transfers: { requested: true },
      },
      business_profile: {
        product_description: 'Workflow automation services',
        mcc: '5734', // Computer software stores
        url: process.env.NEXT_PUBLIC_APP_URL,
      },
      settings: {
        payouts: {
          schedule: {
            interval: 'weekly',
            weekly_anchor: 'friday',
          },
        },
      },
      metadata: {
        userId: userId,
        platform: 'waas-empire',
        createdAt: new Date().toISOString(),
      },
    });

    // Create account link for onboarding
    const accountLink = await stripe.accountLinks.create({
      account: account.id,
      refresh_url: `${process.env.NEXT_PUBLIC_APP_URL}/agency/dashboard?tab=billing&refresh=true`,
      return_url: `${process.env.NEXT_PUBLIC_APP_URL}/agency/dashboard?tab=billing&success=true`,
      type: 'account_onboarding',
    });

    // Store account ID in your database (mock for now)
    await storeStripeAccount(userId, account.id);

    return NextResponse.json({
      success: true,
      accountId: account.id,
      onboardingUrl: accountLink.url,
      message: 'Stripe Express account created successfully'
    });

  } catch (error) {
    console.error('Stripe onboarding error:', error);
    
    if (error instanceof Stripe.errors.StripeError) {
      return NextResponse.json(
        { 
          error: 'Stripe error',
          details: error.message,
          type: error.type
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Get account status
export async function GET(request: NextRequest) {
  try {
    const { userId } = auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get account ID from database (mock for now)
    const accountId = await getStripeAccountId(userId);
    
    if (!accountId) {
      return NextResponse.json({
        connected: false,
        message: 'No Stripe account found'
      });
    }

    // Get account details from Stripe
    const account = await stripe.accounts.retrieve(accountId);

    const isComplete = account.details_submitted && 
                      account.charges_enabled && 
                      account.payouts_enabled;

    return NextResponse.json({
      connected: true,
      accountId: account.id,
      isComplete,
      chargesEnabled: account.charges_enabled,
      payoutsEnabled: account.payouts_enabled,
      detailsSubmitted: account.details_submitted,
      country: account.country,
      currency: account.default_currency,
      businessType: account.business_type,
      requirements: account.requirements,
    });

  } catch (error) {
    console.error('Account status error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to get account status',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Create payment intent with platform fee
export async function PUT(request: NextRequest) {
  try {
    const { userId } = auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { amount, currency = 'usd', connectedAccountId, description } = await request.json();

    if (!amount || !connectedAccountId) {
      return NextResponse.json(
        { error: 'Missing required fields: amount, connectedAccountId' },
        { status: 400 }
      );
    }

    // Calculate platform fee (20%)
    const platformFeeAmount = Math.round(amount * 0.2);

    const paymentIntent = await stripe.paymentIntents.create({
      amount: amount,
      currency: currency,
      application_fee_amount: platformFeeAmount,
      transfer_data: {
        destination: connectedAccountId,
      },
      description: description || 'WaaS Empire workflow payment',
      metadata: {
        userId: userId,
        platformFee: platformFeeAmount.toString(),
        connectedAccount: connectedAccountId,
      },
    });

    return NextResponse.json({
      success: true,
      clientSecret: paymentIntent.client_secret,
      paymentIntentId: paymentIntent.id,
      platformFee: platformFeeAmount,
      netAmount: amount - platformFeeAmount,
    });

  } catch (error) {
    console.error('Payment intent creation error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to create payment intent',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Mock database functions (replace with real database calls)
async function storeStripeAccount(userId: string, accountId: string) {
  // Store in your database
  console.log('Storing Stripe account:', { userId, accountId });
  // For now, we'll use localStorage simulation
  // In production, use a proper database like PostgreSQL, MongoDB, etc.
}

async function getStripeAccountId(userId: string): Promise<string | null> {
  // Retrieve from your database
  console.log('Getting Stripe account for user:', userId);
  // For now, return null (no account found)
  // In production, query your database
  return null;
}

// Webhook handler for Stripe events (separate endpoint recommended)
export async function PATCH(request: NextRequest) {
  try {
    const sig = request.headers.get('stripe-signature');
    const body = await request.text();

    if (!sig || !process.env.STRIPE_WEBHOOK_SECRET) {
      return NextResponse.json(
        { error: 'Missing signature or webhook secret' },
        { status: 400 }
      );
    }

    const event = stripe.webhooks.constructEvent(
      body,
      sig,
      process.env.STRIPE_WEBHOOK_SECRET
    );

    // Handle the event
    switch (event.type) {
      case 'account.updated':
        const account = event.data.object as Stripe.Account;
        console.log('Account updated:', account.id);
        // Update account status in your database
        break;
      
      case 'payment_intent.succeeded':
        const paymentIntent = event.data.object as Stripe.PaymentIntent;
        console.log('Payment succeeded:', paymentIntent.id);
        // Update payment status in your database
        break;

      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    return NextResponse.json({ received: true });

  } catch (error) {
    console.error('Webhook error:', error);
    return NextResponse.json(
      { error: 'Webhook handler failed' },
      { status: 400 }
    );
  }
}
