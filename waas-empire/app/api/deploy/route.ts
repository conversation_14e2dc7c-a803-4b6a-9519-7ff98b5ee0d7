import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-06-30.basil',
});

interface DeployRequest {
  workflowId: string;
  workflowJson: object;
  price: number;
  userApiKeys?: {
    n8nApiKey?: string;
    n8nInstanceUrl?: string;
  };
}

interface N8nWorkflow {
  name: string;
  nodes: any[];
  connections: any;
  active: boolean;
  settings: any;
}

export async function POST(request: NextRequest) {
  try {
    // Authenticate the user
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body: DeployRequest = await request.json();
    const { workflowId, workflowJson, price, userApiKeys } = body;

    // Validate required fields
    if (!workflowId || !workflowJson) {
      return NextResponse.json(
        { error: 'Missing required fields: workflowId, workflowJson' },
        { status: 400 }
      );
    }

    // Get n8n configuration from environment or user input
    const n8nApiKey = userApiKeys?.n8nApiKey || process.env.N8N_API_KEY;
    const n8nInstanceUrl = userApiKeys?.n8nInstanceUrl || process.env.N8N_API_BASE_URL;

    if (!n8nApiKey || !n8nInstanceUrl) {
      return NextResponse.json(
        { error: 'n8n configuration missing. Please provide API key and instance URL.' },
        { status: 400 }
      );
    }

    // Prepare workflow for n8n deployment
    const n8nWorkflow: N8nWorkflow = {
      name: `Marketplace Workflow - ${workflowId}`,
      nodes: (workflowJson as any).nodes || [],
      connections: (workflowJson as any).connections || {},
      active: false, // Start inactive for safety
      settings: {
        executionOrder: 'v1',
        saveManualExecutions: true,
        callerPolicy: 'workflowsFromSameOwner',
        errorWorkflow: '',
        timezone: 'America/New_York'
      }
    };

    // Deploy to n8n instance with retry logic
    let deploymentResult;
    let retryCount = 0;
    const maxRetries = 3;

    while (retryCount < maxRetries) {
      try {
        const response = await fetch(`${n8nInstanceUrl}/api/v1/workflows`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-N8N-API-KEY': n8nApiKey,
          },
          body: JSON.stringify(n8nWorkflow),
        });

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`n8n API error: ${response.status} - ${errorText}`);
        }

        deploymentResult = await response.json();
        break; // Success, exit retry loop

      } catch (error) {
        retryCount++;
        console.error(`Deployment attempt ${retryCount} failed:`, error);
        
        if (retryCount >= maxRetries) {
          // Self-healing: Try alternative deployment method
          try {
            deploymentResult = await fallbackDeployment(n8nInstanceUrl, n8nApiKey, n8nWorkflow);
          } catch (fallbackError) {
            console.error('Fallback deployment failed:', fallbackError);
            return NextResponse.json(
              { 
                error: 'Deployment failed after multiple attempts',
                details: error instanceof Error ? error.message : 'Unknown error',
                retryCount 
              },
              { status: 500 }
            );
          }
        } else {
          // Wait before retry (exponential backoff)
          await new Promise(resolve => setTimeout(resolve, Math.pow(2, retryCount) * 1000));
        }
      }
    }

    // Process payment if price > 0 (50% revenue share)
    let paymentResult = null;
    if (price > 0) {
      try {
        paymentResult = await processMarketplacePayment(userId, workflowId, price);
      } catch (paymentError) {
        console.error('Payment processing failed:', paymentError);
        // Don't fail the deployment for payment issues, just log
      }
    }

    // Log successful deployment
    await logDeployment(userId, workflowId, deploymentResult.id, price);

    return NextResponse.json({
      success: true,
      workflowId: deploymentResult.id,
      message: 'Workflow deployed successfully to n8n',
      n8nWorkflowId: deploymentResult.id,
      paymentProcessed: !!paymentResult,
      retryCount
    });

  } catch (error) {
    console.error('Deployment error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Fallback deployment method for self-healing
async function fallbackDeployment(n8nInstanceUrl: string, apiKey: string, workflow: N8nWorkflow) {
  // Try with simplified workflow structure
  const simplifiedWorkflow = {
    name: workflow.name,
    nodes: workflow.nodes.map(node => ({
      ...node,
      // Remove any problematic properties
      credentials: undefined,
      webhookId: undefined
    })),
    connections: workflow.connections,
    active: false
  };

  const response = await fetch(`${n8nInstanceUrl}/api/v1/workflows`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-N8N-API-KEY': apiKey,
    },
    body: JSON.stringify(simplifiedWorkflow),
  });

  if (!response.ok) {
    throw new Error(`Fallback deployment failed: ${response.status}`);
  }

  return await response.json();
}

// Process marketplace payment with revenue sharing
async function processMarketplacePayment(userId: string, workflowId: string, price: number) {
  try {
    // Get workflow creator's Stripe account (mock for now - would come from database)
    const creatorStripeAccountId = await getWorkflowCreatorStripeAccount(workflowId);

    // Get agency's Stripe account
    const agencyStripeAccountId = await getAgencyStripeAccount(userId);

    if (!creatorStripeAccountId || !agencyStripeAccountId) {
      throw new Error('Missing Stripe Connect accounts for revenue sharing');
    }

    // Calculate revenue shares
    const amountCents = Math.round(price * 100); // Convert to cents
    const platformFeeAmount = Math.round(amountCents * 0.2); // 20% platform fee
    const creatorShareAmount = Math.round(amountCents * 0.5); // 50% to creator
    const agencyShareAmount = amountCents - platformFeeAmount - creatorShareAmount; // Remaining to agency

    // Create payment intent with application fee (platform fee)
    const paymentIntent = await stripe.paymentIntents.create({
      amount: amountCents,
      currency: 'usd',
      application_fee_amount: platformFeeAmount,
      transfer_data: {
        destination: agencyStripeAccountId,
      },
      description: `Workflow deployment: ${workflowId}`,
      metadata: {
        userId,
        workflowId,
        creatorShare: creatorShareAmount.toString(),
        agencyShare: agencyShareAmount.toString(),
        platformFee: platformFeeAmount.toString(),
      },
    });

    // Create transfer to workflow creator (50% share)
    const creatorTransfer = await stripe.transfers.create({
      amount: creatorShareAmount,
      currency: 'usd',
      destination: creatorStripeAccountId,
      description: `Creator revenue share for workflow: ${workflowId}`,
      metadata: {
        workflowId,
        userId,
        shareType: 'creator',
      },
    });

    return {
      paymentId: paymentIntent.id,
      amount: price,
      platformFee: platformFeeAmount / 100,
      creatorShare: creatorShareAmount / 100,
      agencyShare: agencyShareAmount / 100,
      creatorTransferId: creatorTransfer.id,
      status: 'processed'
    };

  } catch (error) {
    console.error('Stripe payment processing error:', error);

    // Fallback to mock response if Stripe fails
    return {
      paymentId: `mock_payment_${Date.now()}`,
      amount: price,
      platformFee: price * 0.2,
      creatorShare: price * 0.5,
      agencyShare: price * 0.3,
      status: 'mock_processed',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

// Mock functions - replace with real database queries
async function getWorkflowCreatorStripeAccount(workflowId: string): Promise<string | null> {
  // In production, query your database for the workflow creator's Stripe account
  console.log('Getting creator Stripe account for workflow:', workflowId);
  return 'acct_mock_creator_123'; // Mock account ID
}

async function getAgencyStripeAccount(userId: string): Promise<string | null> {
  // In production, query your database for the agency's Stripe account
  console.log('Getting agency Stripe account for user:', userId);
  return 'acct_mock_agency_456'; // Mock account ID
}

// Log deployment for analytics
async function logDeployment(userId: string, workflowId: string, n8nWorkflowId: string, price: number) {
  // This would log to your database
  console.log('Deployment logged:', {
    userId,
    workflowId,
    n8nWorkflowId,
    price,
    timestamp: new Date().toISOString()
  });
}

// Health check endpoint
export async function GET() {
  return NextResponse.json({
    status: 'healthy',
    service: 'n8n-deploy-api',
    timestamp: new Date().toISOString()
  });
}
