import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';

interface DeployRequest {
  workflowId: string;
  workflowJson: object;
  price: number;
  userApiKeys?: {
    n8nApiKey?: string;
    n8nInstanceUrl?: string;
  };
}

interface N8nWorkflow {
  name: string;
  nodes: any[];
  connections: any;
  active: boolean;
  settings: any;
}

export async function POST(request: NextRequest) {
  try {
    // Authenticate the user
    const { userId } = auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body: DeployRequest = await request.json();
    const { workflowId, workflowJson, price, userApiKeys } = body;

    // Validate required fields
    if (!workflowId || !workflowJson) {
      return NextResponse.json(
        { error: 'Missing required fields: workflowId, workflowJson' },
        { status: 400 }
      );
    }

    // Get n8n configuration from environment or user input
    const n8nApiKey = userApiKeys?.n8nApiKey || process.env.N8N_API_KEY;
    const n8nInstanceUrl = userApiKeys?.n8nInstanceUrl || process.env.N8N_API_BASE_URL;

    if (!n8nApiKey || !n8nInstanceUrl) {
      return NextResponse.json(
        { error: 'n8n configuration missing. Please provide API key and instance URL.' },
        { status: 400 }
      );
    }

    // Prepare workflow for n8n deployment
    const n8nWorkflow: N8nWorkflow = {
      name: `Marketplace Workflow - ${workflowId}`,
      nodes: (workflowJson as any).nodes || [],
      connections: (workflowJson as any).connections || {},
      active: false, // Start inactive for safety
      settings: {
        executionOrder: 'v1',
        saveManualExecutions: true,
        callerPolicy: 'workflowsFromSameOwner',
        errorWorkflow: '',
        timezone: 'America/New_York'
      }
    };

    // Deploy to n8n instance with retry logic
    let deploymentResult;
    let retryCount = 0;
    const maxRetries = 3;

    while (retryCount < maxRetries) {
      try {
        const response = await fetch(`${n8nInstanceUrl}/api/v1/workflows`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-N8N-API-KEY': n8nApiKey,
          },
          body: JSON.stringify(n8nWorkflow),
        });

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`n8n API error: ${response.status} - ${errorText}`);
        }

        deploymentResult = await response.json();
        break; // Success, exit retry loop

      } catch (error) {
        retryCount++;
        console.error(`Deployment attempt ${retryCount} failed:`, error);
        
        if (retryCount >= maxRetries) {
          // Self-healing: Try alternative deployment method
          try {
            deploymentResult = await fallbackDeployment(n8nInstanceUrl, n8nApiKey, n8nWorkflow);
          } catch (fallbackError) {
            console.error('Fallback deployment failed:', fallbackError);
            return NextResponse.json(
              { 
                error: 'Deployment failed after multiple attempts',
                details: error instanceof Error ? error.message : 'Unknown error',
                retryCount 
              },
              { status: 500 }
            );
          }
        } else {
          // Wait before retry (exponential backoff)
          await new Promise(resolve => setTimeout(resolve, Math.pow(2, retryCount) * 1000));
        }
      }
    }

    // Process payment if price > 0 (50% revenue share)
    let paymentResult = null;
    if (price > 0) {
      try {
        paymentResult = await processMarketplacePayment(userId, workflowId, price);
      } catch (paymentError) {
        console.error('Payment processing failed:', paymentError);
        // Don't fail the deployment for payment issues, just log
      }
    }

    // Log successful deployment
    await logDeployment(userId, workflowId, deploymentResult.id, price);

    return NextResponse.json({
      success: true,
      workflowId: deploymentResult.id,
      message: 'Workflow deployed successfully to n8n',
      n8nWorkflowId: deploymentResult.id,
      paymentProcessed: !!paymentResult,
      retryCount
    });

  } catch (error) {
    console.error('Deployment error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Fallback deployment method for self-healing
async function fallbackDeployment(n8nInstanceUrl: string, apiKey: string, workflow: N8nWorkflow) {
  // Try with simplified workflow structure
  const simplifiedWorkflow = {
    name: workflow.name,
    nodes: workflow.nodes.map(node => ({
      ...node,
      // Remove any problematic properties
      credentials: undefined,
      webhookId: undefined
    })),
    connections: workflow.connections,
    active: false
  };

  const response = await fetch(`${n8nInstanceUrl}/api/v1/workflows`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-N8N-API-KEY': apiKey,
    },
    body: JSON.stringify(simplifiedWorkflow),
  });

  if (!response.ok) {
    throw new Error(`Fallback deployment failed: ${response.status}`);
  }

  return await response.json();
}

// Process marketplace payment with revenue sharing
async function processMarketplacePayment(userId: string, workflowId: string, price: number) {
  // This would integrate with Stripe Connect for revenue sharing
  // For now, return a mock response
  return {
    paymentId: `payment_${Date.now()}`,
    amount: price,
    platformFee: price * 0.2, // 20% platform fee
    creatorShare: price * 0.5, // 50% to workflow creator
    agencyShare: price * 0.3   // 30% to agency
  };
}

// Log deployment for analytics
async function logDeployment(userId: string, workflowId: string, n8nWorkflowId: string, price: number) {
  // This would log to your database
  console.log('Deployment logged:', {
    userId,
    workflowId,
    n8nWorkflowId,
    price,
    timestamp: new Date().toISOString()
  });
}

// Health check endpoint
export async function GET() {
  return NextResponse.json({
    status: 'healthy',
    service: 'n8n-deploy-api',
    timestamp: new Date().toISOString()
  });
}
