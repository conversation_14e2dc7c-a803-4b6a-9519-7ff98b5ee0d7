'use client';

import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Navigation } from '@/components/navigation';

export default function LandingPage() {

  const features = [
    {
      title: "White-Label Solution",
      description: "Complete branding customization for your agency with dynamic theming and logo support.",
      icon: "🎨",
      badge: "Customizable"
    },
    {
      title: "Workflow Marketplace",
      description: "Access thousands of pre-built automation workflows from our curated marketplace.",
      icon: "🛒",
      badge: "1000+ Workflows"
    },
    {
      title: "Revenue Sharing",
      description: "Earn 50% revenue share on marketplace sales with automatic Stripe Connect integration.",
      icon: "💰",
      badge: "50% Share"
    },
    {
      title: "Auto-Deploy",
      description: "One-click deployment to n8n instances with self-healing error handling.",
      icon: "🚀",
      badge: "Instant Deploy"
    },
    {
      title: "Client Management",
      description: "Comprehensive dashboard to manage client accounts and track performance metrics.",
      icon: "👥",
      badge: "Multi-Client"
    },
    {
      title: "Stripe Connect",
      description: "Built-in payment processing with 20% platform fee and automated billing.",
      icon: "💳",
      badge: "Secure Payments"
    }
  ];

  const stats = [
    { label: "Active Agencies", value: "500+" },
    { label: "Workflows Deployed", value: "10K+" },
    { label: "Revenue Generated", value: "$2M+" },
    { label: "Client Satisfaction", value: "99%" }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      <Navigation variant="landing" />

      {/* Hero Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto text-center">
          <div className="mb-8">
            <Badge variant="secondary" className="mb-4">
              🚀 Now in Beta - Join the Revolution
            </Badge>
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              The Ultimate{' '}
              <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                White-Label
              </span>
              <br />
              Workflow Platform
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              Empower your agency with a complete workflow automation platform.
              Deploy, customize, and monetize automation workflows with zero technical overhead.
            </p>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
            <Link href="/waitlist">
              <Button size="lg" className="text-lg px-8 py-6">
                Start Your Agency Today
              </Button>
            </Link>
            <Link href="/marketplace">
              <Button variant="outline" size="lg" className="text-lg px-8 py-6">
                Browse Marketplace
              </Button>
            </Link>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-3xl font-bold text-blue-600 mb-2">{stat.value}</div>
                <div className="text-gray-600">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Everything You Need to Scale
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Built for agencies who want to offer workflow automation without the technical complexity.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow border-0 shadow-md">
                <CardHeader>
                  <div className="flex items-center justify-between mb-2">
                    <div className="text-3xl">{feature.icon}</div>
                    <Badge variant="secondary">{feature.badge}</Badge>
                  </div>
                  <CardTitle className="text-xl">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-gray-600">
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-blue-600 to-purple-600">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Ready to Transform Your Agency?
          </h2>
          <p className="text-xl text-blue-100 mb-8">
            Join hundreds of agencies already using WaaS Empire to deliver world-class automation solutions.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/waitlist">
              <Button size="lg" variant="secondary" className="text-lg px-8 py-6">
                Join the Waitlist
              </Button>
            </Link>
            <Button size="lg" variant="outline" className="text-lg px-8 py-6 text-white border-white hover:bg-white hover:text-blue-600">
              Schedule Demo
            </Button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="col-span-1 md:col-span-2">
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-8 h-8 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">W</span>
                </div>
                <span className="text-xl font-bold">WaaS Empire</span>
              </div>
              <p className="text-gray-400 mb-4 max-w-md">
                The ultimate white-label workflow automation platform for agencies.
                Scale your business with zero technical overhead.
              </p>
            </div>

            <div>
              <h3 className="font-semibold mb-4">Product</h3>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="/marketplace" className="hover:text-white">Marketplace</Link></li>
                <li><Link href="/waitlist" className="hover:text-white">Pricing</Link></li>
                <li><Link href="/waitlist" className="hover:text-white">Documentation</Link></li>
              </ul>
            </div>

            <div>
              <h3 className="font-semibold mb-4">Company</h3>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="/waitlist" className="hover:text-white">About</Link></li>
                <li><Link href="/waitlist" className="hover:text-white">Contact</Link></li>
                <li><Link href="/waitlist" className="hover:text-white">Support</Link></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 WaaS Empire. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
