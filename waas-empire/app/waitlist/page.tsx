'use client';

import { SignUp, useUser } from '@clerk/nextjs';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';

export default function WaitlistPage() {
  const { isSignedIn } = useUser();
  const router = useRouter();
  const [peopleAhead, setPeopleAhead] = useState(428);

  useEffect(() => {
    if (isSignedIn) {
      router.push('/agency/dashboard');
    }
  }, [isSignedIn, router]);

  useEffect(() => {
    // Simulate dynamic counter with slight variations
    const interval = setInterval(() => {
      setPeopleAhead(prev => {
        const change = Math.floor(Math.random() * 3) - 1; // -1, 0, or 1
        const newValue = prev + change;
        return Math.max(420, Math.min(435, newValue)); // Keep between 420-435
      });
    }, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, []);

  if (isSignedIn) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Redirecting to dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-4">
            WaaS Empire
          </h1>
          <p className="text-xl md:text-2xl text-gray-600 mb-2">
            Workflow as a Service Platform
          </p>
          <p className="text-lg text-gray-500">
            The ultimate white-label automation platform for agencies
          </p>
        </div>

        <div className="max-w-6xl mx-auto grid lg:grid-cols-2 gap-12 items-center">
          {/* Left side - Marketing content */}
          <div className="space-y-8">
            <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-200">
              <div className="text-center mb-6">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-orange-100 rounded-full mb-4">
                  <span className="text-2xl font-bold text-orange-600">{peopleAhead}</span>
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-2">
                  People ahead of you
                </h3>
                <p className="text-gray-600">
                  Join the waitlist to get early access to our revolutionary platform
                </p>
              </div>
            </div>

            <div className="space-y-6">
              <h2 className="text-3xl font-bold text-gray-900">
                Why Choose WaaS Empire?
              </h2>

              <div className="space-y-4">
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mt-1">
                    <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900">White-Label Solution</h4>
                    <p className="text-gray-600">Complete branding customization for your agency</p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mt-1">
                    <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900">Workflow Marketplace</h4>
                    <p className="text-gray-600">Access thousands of pre-built automation workflows</p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mt-1">
                    <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900">Revenue Sharing</h4>
                    <p className="text-gray-600">Earn 50% revenue share on marketplace sales</p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mt-1">
                    <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900">Auto-Deploy</h4>
                    <p className="text-gray-600">One-click deployment to n8n instances</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right side - Clerk Sign Up */}
          <div className="flex justify-center">
            <div className="w-full max-w-md">
              <div className="bg-white rounded-2xl shadow-xl border border-gray-200 overflow-hidden">
                <div className="p-6 bg-gradient-to-r from-blue-600 to-indigo-600 text-white text-center">
                  <h3 className="text-2xl font-bold mb-2">Join the Waitlist</h3>
                  <p className="text-blue-100">Get early access to WaaS Empire</p>
                </div>
                <div className="p-6">
                  <SignUp
                    appearance={{
                      elements: {
                        formButtonPrimary:
                          "bg-blue-600 hover:bg-blue-700 text-sm normal-case",
                        card: "shadow-none",
                        headerTitle: "hidden",
                        headerSubtitle: "hidden",
                        socialButtonsBlockButton:
                          "border border-gray-300 hover:bg-gray-50",
                        formFieldInput:
                          "border border-gray-300 focus:border-blue-500 focus:ring-blue-500",
                        footerActionLink: "text-blue-600 hover:text-blue-700"
                      }
                    }}
                    forceRedirectUrl="/agency/dashboard"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center mt-16 text-gray-500">
          <p>&copy; 2024 WaaS Empire. All rights reserved.</p>
        </div>
      </div>
    </div>
  );
}