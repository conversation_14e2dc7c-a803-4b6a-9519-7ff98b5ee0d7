'use client';

import { useState, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';
import { Navigation } from '@/components/navigation';
import { WorkflowCard } from '@/components/workflow-card';
import { CardSkeleton, PageLoading } from '@/components/loading';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface Workflow {
  id: string;
  name: string;
  description: string;
  category: string;
  price: number;
  rating: number;
  downloads: number;
  author: string;
  tags: string[];
  thumbnail?: string;
  workflowJson: object;
}

export default function MarketplacePage() {
  const { isSignedIn, isLoaded } = useUser();
  const router = useRouter();
  const [workflows, setWorkflows] = useState<Workflow[]>([]);
  const [filteredWorkflows, setFilteredWorkflows] = useState<Workflow[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [loading, setLoading] = useState(true);
  const [deployingId, setDeployingId] = useState<string | null>(null);

  // Mock data - In real implementation, this would fetch from Google Sheets API
  const mockWorkflows: Workflow[] = [
    {
      id: '1',
      name: 'Lead Generation Automation',
      description: 'Automatically capture leads from multiple sources and add them to your CRM',
      category: 'Marketing',
      price: 49.99,
      rating: 4.8,
      downloads: 1250,
      author: 'AutomationPro',
      tags: ['CRM', 'Lead Generation', 'Email'],
      workflowJson: { nodes: [], connections: [] }
    },
    {
      id: '2',
      name: 'Social Media Scheduler',
      description: 'Schedule and post content across multiple social media platforms',
      category: 'Social Media',
      price: 29.99,
      rating: 4.6,
      downloads: 890,
      author: 'SocialGuru',
      tags: ['Social Media', 'Scheduling', 'Content'],
      workflowJson: { nodes: [], connections: [] }
    },
    {
      id: '3',
      name: 'Invoice Processing System',
      description: 'Automatically process invoices and update accounting systems',
      category: 'Finance',
      price: 79.99,
      rating: 4.9,
      downloads: 567,
      author: 'FinanceFlow',
      tags: ['Accounting', 'Invoices', 'Automation'],
      workflowJson: { nodes: [], connections: [] }
    },
    {
      id: '4',
      name: 'Customer Support Chatbot',
      description: 'AI-powered chatbot for handling customer inquiries',
      category: 'Customer Service',
      price: 99.99,
      rating: 4.7,
      downloads: 2100,
      author: 'ChatMaster',
      tags: ['AI', 'Chatbot', 'Support'],
      workflowJson: { nodes: [], connections: [] }
    },
    {
      id: '5',
      name: 'Email Marketing Campaign',
      description: 'Create and manage automated email marketing campaigns',
      category: 'Marketing',
      price: 39.99,
      rating: 4.5,
      downloads: 1800,
      author: 'EmailExpert',
      tags: ['Email', 'Marketing', 'Campaigns'],
      workflowJson: { nodes: [], connections: [] }
    },
    {
      id: '6',
      name: 'Data Backup Automation',
      description: 'Automatically backup important data to multiple cloud services',
      category: 'Utilities',
      price: 19.99,
      rating: 4.4,
      downloads: 445,
      author: 'BackupBuddy',
      tags: ['Backup', 'Cloud', 'Data'],
      workflowJson: { nodes: [], connections: [] }
    }
  ];

  const categories = ['all', 'Marketing', 'Social Media', 'Finance', 'Customer Service', 'Utilities'];

  useEffect(() => {
    if (isLoaded && !isSignedIn) {
      router.push('/waitlist');
    }
  }, [isSignedIn, isLoaded, router]);

  useEffect(() => {
    // Simulate API call to Google Sheets
    const fetchWorkflows = async () => {
      setLoading(true);
      // In real implementation:
      // const response = await fetch('/api/workflows');
      // const data = await response.json();
      
      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setWorkflows(mockWorkflows);
      setFilteredWorkflows(mockWorkflows);
      setLoading(false);
    };

    if (isSignedIn) {
      fetchWorkflows();
    }
  }, [isSignedIn]);

  useEffect(() => {
    let filtered = workflows;

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(workflow => workflow.category === selectedCategory);
    }

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(workflow =>
        workflow.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        workflow.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        workflow.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    setFilteredWorkflows(filtered);
  }, [workflows, selectedCategory, searchTerm]);

  const handleDeploy = async (workflow: Workflow) => {
    setDeployingId(workflow.id);
    
    try {
      // In real implementation, this would call the deploy API
      const response = await fetch('/api/deploy', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          workflowId: workflow.id,
          workflowJson: workflow.workflowJson,
          price: workflow.price
        }),
      });

      if (response.ok) {
        alert(`Successfully deployed "${workflow.name}" to your n8n instance!`);
      } else {
        throw new Error('Deployment failed');
      }
    } catch (error) {
      alert('Failed to deploy workflow. Please try again.');
      console.error('Deployment error:', error);
    } finally {
      setDeployingId(null);
    }
  };

  if (!isLoaded || !isSignedIn) {
    return <PageLoading text="Loading marketplace..." />;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation variant="dashboard" />

      {/* Page Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Workflow Marketplace</h1>
              <p className="mt-2 text-gray-600">Discover and deploy automation workflows to your n8n instance</p>
            </div>
            <Button
              onClick={() => router.push('/agency/dashboard')}
              variant="outline"
            >
              <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
              </svg>
              Back to Dashboard
            </Button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Search and Filters */}
        <div className="mb-8">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
            {/* Search */}
            <div className="flex-1 max-w-lg">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg className="h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
                  </svg>
                </div>
                <Input
                  type="text"
                  placeholder="Search workflows..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            {/* Category Filter */}
            <div className="flex items-center space-x-4">
              <label className="text-sm font-medium text-gray-700">Category:</label>
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map(category => (
                    <SelectItem key={category} value={category}>
                      {category === 'all' ? 'All Categories' : category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* Results Count */}
        <div className="mb-6">
          <p className="text-gray-600">
            {loading ? 'Loading...' : `${filteredWorkflows.length} workflow${filteredWorkflows.length !== 1 ? 's' : ''} found`}
          </p>
        </div>

        {/* Workflows Grid */}
        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <CardSkeleton key={i} />
            ))}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredWorkflows.map((workflow) => (
              <WorkflowCard
                key={workflow.id}
                workflow={workflow}
                onDeploy={handleDeploy}
                isDeploying={deployingId === workflow.id}
              />
            ))}
          </div>
        )}

        {/* Empty State */}
        {!loading && filteredWorkflows.length === 0 && (
          <div className="text-center py-12">
            <svg className="w-16 h-16 mx-auto text-gray-300 mb-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
            </svg>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No workflows found</h3>
            <p className="text-gray-500">Try adjusting your search or filter criteria.</p>
          </div>
        )}
      </div>
    </div>
  );
}
