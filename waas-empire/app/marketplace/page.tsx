'use client';

import { useState, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';

interface Workflow {
  id: string;
  name: string;
  description: string;
  category: string;
  price: number;
  rating: number;
  downloads: number;
  author: string;
  tags: string[];
  thumbnail: string;
  workflowJson: object;
}

export default function MarketplacePage() {
  const { isSignedIn, isLoaded } = useUser();
  const router = useRouter();
  const [workflows, setWorkflows] = useState<Workflow[]>([]);
  const [filteredWorkflows, setFilteredWorkflows] = useState<Workflow[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [loading, setLoading] = useState(true);
  const [deployingId, setDeployingId] = useState<string | null>(null);

  // Mock data - In real implementation, this would fetch from Google Sheets API
  const mockWorkflows: Workflow[] = [
    {
      id: '1',
      name: 'Lead Generation Automation',
      description: 'Automatically capture leads from multiple sources and add them to your CRM',
      category: 'Marketing',
      price: 49.99,
      rating: 4.8,
      downloads: 1250,
      author: 'AutomationPro',
      tags: ['CRM', 'Lead Generation', 'Email'],
      thumbnail: '/api/placeholder/300/200',
      workflowJson: { nodes: [], connections: [] }
    },
    {
      id: '2',
      name: 'Social Media Scheduler',
      description: 'Schedule and post content across multiple social media platforms',
      category: 'Social Media',
      price: 29.99,
      rating: 4.6,
      downloads: 890,
      author: 'SocialGuru',
      tags: ['Social Media', 'Scheduling', 'Content'],
      thumbnail: '/api/placeholder/300/200',
      workflowJson: { nodes: [], connections: [] }
    },
    {
      id: '3',
      name: 'Invoice Processing System',
      description: 'Automatically process invoices and update accounting systems',
      category: 'Finance',
      price: 79.99,
      rating: 4.9,
      downloads: 567,
      author: 'FinanceFlow',
      tags: ['Accounting', 'Invoices', 'Automation'],
      thumbnail: '/api/placeholder/300/200',
      workflowJson: { nodes: [], connections: [] }
    },
    {
      id: '4',
      name: 'Customer Support Chatbot',
      description: 'AI-powered chatbot for handling customer inquiries',
      category: 'Customer Service',
      price: 99.99,
      rating: 4.7,
      downloads: 2100,
      author: 'ChatMaster',
      tags: ['AI', 'Chatbot', 'Support'],
      thumbnail: '/api/placeholder/300/200',
      workflowJson: { nodes: [], connections: [] }
    },
    {
      id: '5',
      name: 'Email Marketing Campaign',
      description: 'Create and manage automated email marketing campaigns',
      category: 'Marketing',
      price: 39.99,
      rating: 4.5,
      downloads: 1800,
      author: 'EmailExpert',
      tags: ['Email', 'Marketing', 'Campaigns'],
      thumbnail: '/api/placeholder/300/200',
      workflowJson: { nodes: [], connections: [] }
    },
    {
      id: '6',
      name: 'Data Backup Automation',
      description: 'Automatically backup important data to multiple cloud services',
      category: 'Utilities',
      price: 19.99,
      rating: 4.4,
      downloads: 445,
      author: 'BackupBuddy',
      tags: ['Backup', 'Cloud', 'Data'],
      thumbnail: '/api/placeholder/300/200',
      workflowJson: { nodes: [], connections: [] }
    }
  ];

  const categories = ['all', 'Marketing', 'Social Media', 'Finance', 'Customer Service', 'Utilities'];

  useEffect(() => {
    if (isLoaded && !isSignedIn) {
      router.push('/waitlist');
    }
  }, [isSignedIn, isLoaded, router]);

  useEffect(() => {
    // Simulate API call to Google Sheets
    const fetchWorkflows = async () => {
      setLoading(true);
      // In real implementation:
      // const response = await fetch('/api/workflows');
      // const data = await response.json();
      
      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setWorkflows(mockWorkflows);
      setFilteredWorkflows(mockWorkflows);
      setLoading(false);
    };

    if (isSignedIn) {
      fetchWorkflows();
    }
  }, [isSignedIn]);

  useEffect(() => {
    let filtered = workflows;

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(workflow => workflow.category === selectedCategory);
    }

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(workflow =>
        workflow.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        workflow.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        workflow.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    setFilteredWorkflows(filtered);
  }, [workflows, selectedCategory, searchTerm]);

  const handleDeploy = async (workflow: Workflow) => {
    setDeployingId(workflow.id);
    
    try {
      // In real implementation, this would call the deploy API
      const response = await fetch('/api/deploy', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          workflowId: workflow.id,
          workflowJson: workflow.workflowJson,
          price: workflow.price
        }),
      });

      if (response.ok) {
        alert(`Successfully deployed "${workflow.name}" to your n8n instance!`);
      } else {
        throw new Error('Deployment failed');
      }
    } catch (error) {
      alert('Failed to deploy workflow. Please try again.');
      console.error('Deployment error:', error);
    } finally {
      setDeployingId(null);
    }
  };

  if (!isLoaded || !isSignedIn) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading marketplace...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => router.push('/agency/dashboard')}
                className="text-blue-600 hover:text-blue-800 flex items-center"
              >
                <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
                </svg>
                Back to Dashboard
              </button>
            </div>
            <h1 className="text-2xl font-bold text-gray-900">Workflow Marketplace</h1>
            <div className="w-32"></div> {/* Spacer for centering */}
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Search and Filters */}
        <div className="mb-8">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
            {/* Search */}
            <div className="flex-1 max-w-lg">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg className="h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
                  </svg>
                </div>
                <input
                  type="text"
                  placeholder="Search workflows..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>

            {/* Category Filter */}
            <div className="flex items-center space-x-4">
              <label className="text-sm font-medium text-gray-700">Category:</label>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="border border-gray-300 rounded-md px-3 py-2 bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              >
                {categories.map(category => (
                  <option key={category} value={category}>
                    {category === 'all' ? 'All Categories' : category}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Results Count */}
        <div className="mb-6">
          <p className="text-gray-600">
            {loading ? 'Loading...' : `${filteredWorkflows.length} workflow${filteredWorkflows.length !== 1 ? 's' : ''} found`}
          </p>
        </div>

        {/* Workflows Grid */}
        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="bg-white rounded-lg shadow-md p-6 animate-pulse">
                <div className="h-40 bg-gray-300 rounded mb-4"></div>
                <div className="h-4 bg-gray-300 rounded mb-2"></div>
                <div className="h-3 bg-gray-300 rounded mb-4"></div>
                <div className="h-8 bg-gray-300 rounded"></div>
              </div>
            ))}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredWorkflows.map((workflow) => (
              <div key={workflow.id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                <div className="h-48 bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center">
                  <div className="text-white text-center">
                    <svg className="w-16 h-16 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
                    </svg>
                    <p className="text-sm font-medium">{workflow.category}</p>
                  </div>
                </div>
                
                <div className="p-6">
                  <div className="flex items-start justify-between mb-2">
                    <h3 className="text-lg font-semibold text-gray-900 line-clamp-1">
                      {workflow.name}
                    </h3>
                    <span className="text-lg font-bold text-green-600">
                      ${workflow.price}
                    </span>
                  </div>
                  
                  <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                    {workflow.description}
                  </p>
                  
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <div className="flex items-center">
                        <svg className="w-4 h-4 text-yellow-400 mr-1" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                        {workflow.rating}
                      </div>
                      <div>{workflow.downloads} downloads</div>
                    </div>
                  </div>
                  
                  <div className="flex flex-wrap gap-1 mb-4">
                    {workflow.tags.slice(0, 3).map((tag) => (
                      <span key={tag} className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
                        {tag}
                      </span>
                    ))}
                  </div>
                  
                  <button
                    onClick={() => handleDeploy(workflow)}
                    disabled={deployingId === workflow.id}
                    className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                  >
                    {deployingId === workflow.id ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Deploying...
                      </>
                    ) : (
                      'Deploy to n8n'
                    )}
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Empty State */}
        {!loading && filteredWorkflows.length === 0 && (
          <div className="text-center py-12">
            <svg className="w-16 h-16 mx-auto text-gray-300 mb-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
            </svg>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No workflows found</h3>
            <p className="text-gray-500">Try adjusting your search or filter criteria.</p>
          </div>
        )}
      </div>
    </div>
  );
}
