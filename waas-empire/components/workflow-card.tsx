'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

interface Workflow {
  id: string;
  name: string;
  description: string;
  category: string;
  price: number;
  rating: number;
  downloads: number;
  author: string;
  tags: string[];
  thumbnail?: string;
  workflowJson: object;
}

interface WorkflowCardProps {
  workflow: Workflow;
  onDeploy?: (workflow: Workflow) => void;
  isDeploying?: boolean;
  variant?: 'default' | 'compact';
}

export function WorkflowCard({ 
  workflow, 
  onDeploy, 
  isDeploying = false,
  variant = 'default'
}: WorkflowCardProps) {
  const [imageError, setImageError] = useState(false);

  const handleDeploy = () => {
    if (onDeploy && !isDeploying) {
      onDeploy(workflow);
    }
  };

  const getCategoryIcon = (category: string) => {
    const icons: Record<string, string> = {
      'Marketing': '📈',
      'Social Media': '📱',
      'Finance': '💰',
      'Customer Service': '🎧',
      'Utilities': '🔧',
      'E-commerce': '🛒',
      'Analytics': '📊',
      'Communication': '💬',
      'Productivity': '⚡',
      'Integration': '🔗'
    };
    return icons[category] || '⚙️';
  };

  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      'Marketing': 'from-green-400 to-blue-500',
      'Social Media': 'from-pink-400 to-purple-500',
      'Finance': 'from-yellow-400 to-orange-500',
      'Customer Service': 'from-blue-400 to-indigo-500',
      'Utilities': 'from-gray-400 to-gray-600',
      'E-commerce': 'from-purple-400 to-pink-500',
      'Analytics': 'from-indigo-400 to-blue-500',
      'Communication': 'from-green-400 to-teal-500',
      'Productivity': 'from-orange-400 to-red-500',
      'Integration': 'from-teal-400 to-cyan-500'
    };
    return colors[category] || 'from-blue-400 to-purple-500';
  };

  if (variant === 'compact') {
    return (
      <Card className="hover:shadow-md transition-shadow cursor-pointer">
        <CardContent className="p-4">
          <div className="flex items-center space-x-3">
            <div className={`w-12 h-12 bg-gradient-to-br ${getCategoryColor(workflow.category)} rounded-lg flex items-center justify-center text-white text-xl`}>
              {getCategoryIcon(workflow.category)}
            </div>
            
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-sm truncate">{workflow.name}</h3>
              <p className="text-xs text-gray-600 truncate">{workflow.description}</p>
              <div className="flex items-center space-x-2 mt-1">
                <Badge variant="secondary" className="text-xs">
                  {workflow.category}
                </Badge>
                <span className="text-sm font-bold text-green-600">
                  ${workflow.price}
                </span>
              </div>
            </div>
            
            <Button 
              size="sm" 
              onClick={handleDeploy}
              disabled={isDeploying}
              className="shrink-0"
            >
              {isDeploying ? 'Deploying...' : 'Deploy'}
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="overflow-hidden hover:shadow-lg transition-shadow">
      {/* Thumbnail */}
      <div className={`h-48 bg-gradient-to-br ${getCategoryColor(workflow.category)} flex items-center justify-center relative`}>
        {workflow.thumbnail && !imageError ? (
          <img
            src={workflow.thumbnail}
            alt={workflow.name}
            className="w-full h-full object-cover"
            onError={() => setImageError(true)}
          />
        ) : (
          <div className="text-white text-center">
            <div className="text-4xl mb-2">{getCategoryIcon(workflow.category)}</div>
            <p className="text-sm font-medium">{workflow.category}</p>
          </div>
        )}
        
        {/* Category badge */}
        <Badge 
          variant="secondary" 
          className="absolute top-2 left-2 bg-white/90 text-gray-800"
        >
          {workflow.category}
        </Badge>
        
        {/* Price badge */}
        <Badge 
          className="absolute top-2 right-2 bg-green-600 text-white"
        >
          ${workflow.price}
        </Badge>
      </div>
      
      <CardHeader className="pb-2">
        <div className="flex items-start justify-between">
          <CardTitle className="text-lg line-clamp-1">
            {workflow.name}
          </CardTitle>
          <div className="flex items-center space-x-1 text-sm text-gray-500">
            <svg className="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
            </svg>
            <span>{workflow.rating}</span>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        <p className="text-gray-600 text-sm mb-4 line-clamp-2">
          {workflow.description}
        </p>
        
        {/* Author and stats */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <Avatar className="w-6 h-6">
              <AvatarImage src={`https://api.dicebear.com/7.x/initials/svg?seed=${workflow.author}`} />
              <AvatarFallback className="text-xs">
                {workflow.author.slice(0, 2).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <span className="text-sm text-gray-600">{workflow.author}</span>
          </div>
          
          <div className="text-sm text-gray-500">
            {workflow.downloads.toLocaleString()} downloads
          </div>
        </div>
        
        {/* Tags */}
        <div className="flex flex-wrap gap-1 mb-4">
          {workflow.tags.slice(0, 3).map((tag) => (
            <Badge key={tag} variant="outline" className="text-xs">
              {tag}
            </Badge>
          ))}
          {workflow.tags.length > 3 && (
            <Badge variant="outline" className="text-xs">
              +{workflow.tags.length - 3} more
            </Badge>
          )}
        </div>
        
        {/* Deploy button */}
        <Button
          onClick={handleDeploy}
          disabled={isDeploying}
          className="w-full"
        >
          {isDeploying ? (
            <>
              <svg className="animate-spin -ml-1 mr-3 h-4 w-4" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Deploying...
            </>
          ) : (
            'Deploy to n8n'
          )}
        </Button>
      </CardContent>
    </Card>
  );
}
